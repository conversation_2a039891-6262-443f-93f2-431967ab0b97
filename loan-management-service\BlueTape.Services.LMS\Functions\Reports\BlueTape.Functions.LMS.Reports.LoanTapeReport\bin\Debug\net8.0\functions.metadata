[{"name": "LoanTapeReport", "scriptFile": "BlueTape.Functions.LMS.Reports.LoanTapeReport.dll", "entryPoint": "BlueTape.Functions.LMS.Reports.LoanTapeReport.LoanTapeReport.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 0 7 * * *", "properties": {}}]}, {"name": "MissingLoanTapeReportFilesJob", "scriptFile": "BlueTape.Functions.LMS.Reports.LoanTapeReport.dll", "entryPoint": "BlueTape.Functions.LMS.Reports.LoanTapeReport.MissingLoanTapeReportFiles.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 0 10 * * *", "properties": {}}]}]