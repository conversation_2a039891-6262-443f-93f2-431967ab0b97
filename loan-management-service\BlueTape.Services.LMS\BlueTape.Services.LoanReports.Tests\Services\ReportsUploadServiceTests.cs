﻿using Amazon;
using BlueTape.AWSS3;
using BlueTape.AWSS3.Abstractions;
using BlueTape.Services.LMS.Application.Tests.Attributes;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Services.LoanReports.Tests.TestData;
using BlueTape.Services.Reporting.Domain.Constants;
using BlueTape.Services.Reporting.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Shouldly;

namespace BlueTape.Services.LoanReports.Tests.Services;

public class ReportsUploadServiceTests
{
    private readonly ReportsUploadService _reportsUploadService;

    private readonly Mock<IDynamicS3Client> _s3ClientMock = new();
    private readonly Mock<IConfiguration> _configuration = new();
    private readonly Mock<IOptions<S3ConfigurationOptions>> _options = new();
    private readonly Mock<ILogger<ReportsUploadService>> _loggerMock = new();

    private static readonly string BucketName = ConfigurationData.S3ConfigurationOptionsData.S3BucketName!;

    public ReportsUploadServiceTests()
    {
        _options.Setup(x => x.Value).Returns(ConfigurationData.S3ConfigurationOptionsData);
        _configuration.Setup(config => config[ConfigurationData.S3ConfigurationOptionsData.ArcadiaS3BucketSecretKey!]).Returns(ConfigurationData.ArcadiaS3BucketSecretKeyValue);
        _configuration.Setup(config => config[ConfigurationData.S3ConfigurationOptionsData.ArcadiaS3BucketAccessKey!]).Returns(ConfigurationData.ArcadiaS3BucketAccessKeyValue);
        _reportsUploadService = new ReportsUploadService(_s3ClientMock.Object, _options.Object, _configuration.Object, _loggerMock.Object);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UploadLoanTapeToS3_NonProvEnv_UploadsFileToBlueTapeS3Only(byte[] fileBytes)
    {
        var fileName = Guid.NewGuid().ToString();
        var env = EnvironmentConstants.Dev;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        await _reportsUploadService.UploadLoanTapeToS3(fileBytes, fileName, default);

        _s3ClientMock.Verify(x => x.SaveBinaryAsync(fileBytes, fileName, $"{env}.{BucketName}", It.Is<S3ConnectionOptions>(
            connectionOptions => connectionOptions.RegionEndpoint == RegionEndpoint.USWest1), default), Times.Once);
        _s3ClientMock.Verify(x => x.SaveBinaryAsync(fileBytes, fileName, $"{env}.{BucketName}", It.Is<S3ConnectionOptions>(
            connectionOptions => connectionOptions.RegionEndpoint == RegionEndpoint.USEast1), default), Times.Never);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UploadLoanTapeToS3_ProvEnv_UploadsFileToBlueTapeS3AndArcadiaBuckets(byte[] fileBytes)
    {
        var fileName = LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix;
        var env = EnvironmentConstants.Production;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        await _reportsUploadService.UploadLoanTapeToS3(fileBytes, fileName, default);

        _s3ClientMock.Verify(x => x.SaveBinaryAsync(fileBytes, fileName, $"{env}.{BucketName}", It.Is<S3ConnectionOptions>(
            connectionOptions => connectionOptions.RegionEndpoint == RegionEndpoint.USWest1), default), Times.Once);

        _s3ClientMock.Verify(x => x.SaveBinaryAsync(fileBytes, fileName, ConfigurationData.S3ConfigurationOptionsData.ArcadiaS3BucketName!,
            It.Is<S3ConnectionOptions>(
            connectionOptions => connectionOptions.RegionEndpoint == RegionEndpoint.USEast1 &&
            connectionOptions.AccessKey == ConfigurationData.ArcadiaS3BucketAccessKeyValue &&
            connectionOptions.SecretKey == ConfigurationData.ArcadiaS3BucketSecretKeyValue),
            default), Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UploadLoanTapeToS3_ProvEnv_UploadsManualPaymentsReportFileToDwhBucket(byte[] fileBytes)
    {
        var fileName = $"{LoanTapeReportConstants.ManualPaymentsReportFileNamePrefix}.csv";
        var env = EnvironmentConstants.Production;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        await _reportsUploadService.UploadLoanTapeToS3(fileBytes, fileName, default);

        _s3ClientMock.Verify(x => x.SaveBinaryAsync(fileBytes, fileName, "dwh-assets", It.Is<S3ConnectionOptions>(
            connectionOptions => connectionOptions.RegionEndpoint == RegionEndpoint.USWest1), default), Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task UploadLoanTapeToS3_ProvEnv_UploadsFeesFileToDwhBucket(byte[] fileBytes)
    {
        var fileName = LoanTapeReportConstants.DrawsFeeReportFileNamePrefix;
        var env = EnvironmentConstants.Production;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        await _reportsUploadService.UploadLoanTapeToS3(fileBytes, fileName, default);

        _s3ClientMock.Verify(x => x.SaveBinaryAsync(fileBytes, fileName, "dwh-assets", It.Is<S3ConnectionOptions>(
            connectionOptions => connectionOptions.RegionEndpoint == RegionEndpoint.USWest1), default), Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public Task UploadLoanTapeToS3_EmptyS3BlueTapeBucketName_ThrowsVariableNullException(byte[] fileBytes)
    {
        var fileName = Guid.NewGuid().ToString();
        var env = EnvironmentConstants.Production;
        _options.Setup(x => x.Value).Returns(ConfigurationData.S3ConfigurationOptionsDataWithEmptyBlueTapeBucketName);
        var reportsUploadService = new ReportsUploadService(_s3ClientMock.Object, _options.Object, _configuration.Object, _loggerMock.Object);

        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        var act = async () => await reportsUploadService.UploadLoanTapeToS3(fileBytes, fileName, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, AutoDataWithDateOnly]
    public Task UploadLoanTapeToS3_EmptyArcadiaBucketName_ThrowsVariableNullException(byte[] fileBytes)
    {
        var fileName = Guid.NewGuid().ToString();
        var env = EnvironmentConstants.Production;
        _options.Setup(x => x.Value).Returns(ConfigurationData.S3ConfigurationOptionsDataWithEmptyArcadiaBucketName);
        _configuration.Setup(config => config[ConfigurationData.S3ConfigurationOptionsData.ArcadiaS3BucketSecretKey!]).Returns((string?)null);
        _configuration.Setup(config => config[ConfigurationData.S3ConfigurationOptionsData.ArcadiaS3BucketAccessKey!]).Returns((string?)null);

        var reportsUploadService = new ReportsUploadService(_s3ClientMock.Object, _options.Object, _configuration.Object, _loggerMock.Object);

        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        var act = async () => await reportsUploadService.UploadLoanTapeToS3(fileBytes, fileName, default);

        return act.ShouldThrowAsync<VariableNullException>();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task IsFileExistInS3Bucket_FileExists_ReturnsTrue(string fileName)
    {
        var env = EnvironmentConstants.Dev;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                $"{env}.{BucketName}",
                fileName,
                It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
                default))
            .ReturnsAsync(true);

        var result = await _reportsUploadService.IsFileExistInS3Bucket(fileName, default);

        result.ShouldBeTrue();
        _s3ClientMock.Verify(x => x.IsFileExistInS3BucketAsync(
            $"{env}.{BucketName}",
            fileName,
            It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
            default), Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task IsFileExistInS3Bucket_FileDoesNotExist_ReturnsFalse(string fileName)
    {
        var env = EnvironmentConstants.Dev;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                $"{env}.{BucketName}",
                fileName,
                It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
                default))
            .ReturnsAsync(false);

        var result = await _reportsUploadService.IsFileExistInS3Bucket(fileName, default);

        result.ShouldBeFalse();
        _s3ClientMock.Verify(x => x.IsFileExistInS3BucketAsync(
            $"{env}.{BucketName}",
            fileName,
            It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
            default), Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task IsFileExistInS3Bucket_ProductionEnvironmentWithDwhFile_ChecksInDwhBucket(string fileName)
    {
        var env = EnvironmentConstants.Production;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        fileName = $"{LoanTapeReportConstants.ManualPaymentsReportFileNamePrefix}.csv";

        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                "dwh-assets", // should use DWH bucket for DWH files in production
                fileName,
                It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
                default))
            .ReturnsAsync(true);

        var result = await _reportsUploadService.IsFileExistInS3Bucket(fileName, default);

        result.ShouldBeTrue();
        _s3ClientMock.Verify(x => x.IsFileExistInS3BucketAsync(
            "dwh-assets",
            fileName,
            It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
            default), Times.Once);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task IsFileExistInS3Bucket_ThrowsException_ReturnsFalse(string fileName)
    {
        var env = EnvironmentConstants.Dev;
        Environment.SetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment, env);

        _s3ClientMock
            .Setup(x => x.IsFileExistInS3BucketAsync(
                $"{env}.{BucketName}",
                fileName,
                It.Is<S3ConnectionOptions>(options => options.RegionEndpoint == RegionEndpoint.USWest1),
                default))
            .ThrowsAsync(new Exception("S3 error"));

        var result = await _reportsUploadService.IsFileExistInS3Bucket(fileName, default);

        result.ShouldBeFalse();
        _loggerMock.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error checking if file {fileName} exists in S3")),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

}
