using BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Extensions;
using BlueTape.PaymentService.CompatibilityService.Services.Base;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Enums.Legacy;
using BlueTape.PaymentService.Domain.Extensions;
using MongoDB.Bson;

namespace BlueTape.PaymentService.CompatibilityService.Services.SubscriptionFee;

public class SubscriptionFeeCompatibilityMapper(IBankAccountRepository bankAccountRepository)
    : BaseCompatibilityMapper(bankAccountRepository), ISubscriptionFeeCompatibilityMapper
{
    public OperationEntity MapFromPaymentRequestToSubscriptionFeeOperation(PaymentRequestEntity paymentRequest)
    {
        var payable = paymentRequest.PaymentRequestPayables.FirstOrDefault();
        var operationStatus = paymentRequest.GetPaymentOperationStatus().ToString();

        return new OperationEntity
        {
            BlueTapeId = ObjectId.GenerateNewId().ToString()!,
            OwnerId = payable?.PayableId,
            Status = operationStatus,
            Type = LegacyPaymentFlowConstants.SubscriptionFeePayment,
            Amount = paymentRequest.Amount,
            Date = paymentRequest.Date.ToDateTime(new TimeOnly(), DateTimeKind.Utc),
            PaymentRequestId = paymentRequest.Id.ToString(),
            CreatedBy = DomainConstants.PaymentService,
            PaymentProvider = PaymentProvider.Aion.ToString(),
            Metadata = new OperationMetadataEntity
            {
                PayeeId = paymentRequest.PayeeId,
                PayerId = paymentRequest.PayerId,
                PaymentMethod = "ach",
            }
        };
    }

    public async Task<TransactionEntity> MapFromPaymentTransactionToSubscriptionFeeLegacyTransaction(
        PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken cancellationToken)
    {
        var transaction = paymentRequest.Transactions
            .Where(x => x.TransactionType == PaymentTransactionType.AchPull)
            .OrderByDescending(x => x.SequenceNumber)
            .ThenByDescending(e => e.CreatedAt)
            .FirstOrDefault() ?? paymentRequest.Transactions.Last();

        var legacyTransactionAmount = operation.Amount;
        var metadata = await GetTransactionMetadata(transaction, legacyTransactionAmount, cancellationToken);

        var legacyTransaction = new TransactionEntity
        {
            OperationId = operation.BlueTapeId,
            Type = LegacyTransactionType.PULL.ToString().ToLower(),
            PayeeId = paymentRequest.PayeeId,
            PayerId = paymentRequest.PayerId,
            Amount = legacyTransactionAmount,
            Currency = transaction.Currency,
            PaymentMethod = PaymentMethod.Ach.ToString().ToLower(),
            Reason = $"Subscription Fee Payment: {paymentRequest.PaymentRequestDetails?.Reason}",
            Status = transaction.Status.MapFromPaymentTransactionStatusToLegacyTransactionStatus().ToString(),
            CreatedBy = DomainConstants.PaymentService,
            Provider = PaymentProvider.Aion.ToString(),
            PaymentTransactionId = transaction.Id.ToString(),
            Date = transaction.Date.ToDateTime(new TimeOnly()),
            CreatedAt = DateTime.UtcNow,
            Metadata = metadata,
        };

        return legacyTransaction;
    }
}
