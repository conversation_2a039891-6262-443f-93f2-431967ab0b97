using BlueTape.PaymentService.CompatibilityService.Services.SubscriptionFee;
using BlueTape.PaymentService.CompatibilityService.Tests.Entities;
using BlueTape.PaymentService.DataAccess.Mongo.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Constants;
using BlueTape.PaymentService.Domain.Enums;
using NSubstitute;
using Shouldly;
using Xunit;

namespace BlueTape.PaymentService.CompatibilityService.Tests.Services.SubscriptionFee;

public class SubscriptionFeeCompatibilityMapperTests
{
    private readonly SubscriptionFeeCompatibilityMapper _mapper;
    private readonly IBankAccountRepository _bankAccountRepository = Substitute.For<IBankAccountRepository>();

    public SubscriptionFeeCompatibilityMapperTests()
    {
        _mapper = new SubscriptionFeeCompatibilityMapper(_bankAccountRepository);
    }

    [Fact]
    public void MapFromPaymentRequestToSubscriptionFeeOperation_ValidPaymentRequest_ReturnsCorrectOperation()
    {
        // Arrange
        var paymentRequest = PaymentRequestEntities.PaymentRequestToPerformOperation;

        // Act
        var result = _mapper.MapFromPaymentRequestToSubscriptionFeeOperation(paymentRequest);

        // Assert
        result.ShouldNotBeNull();
        result.BlueTapeId.ShouldNotBeNullOrEmpty();
        result.OwnerId.ShouldBe(paymentRequest.PaymentRequestPayables.FirstOrDefault().PayableId ?? string.Empty);
        result.Type.ShouldBe(LegacyPaymentFlowConstants.SubscriptionFeePayment);
        result.Amount.ShouldBe(paymentRequest.Amount);
        result.PaymentRequestId.ShouldBe(paymentRequest.Id.ToString());
        result.CreatedBy.ShouldBe(DomainConstants.PaymentService);
        result.PaymentProvider.ShouldBe(PaymentProvider.Aion.ToString());
        result.Metadata.ShouldNotBeNull();
        result.Metadata.PayeeId.ShouldBe(paymentRequest.PayeeId);
        result.Metadata.PayerId.ShouldBe(paymentRequest.PayerId);
        result.Metadata.PaymentMethod.ShouldBe("ach");
        result.Metadata.LmsPaymentId.ShouldBe(paymentRequest.PaymentRequestDetails?.LMSPaymentId.ToString());
    }

    [Fact]
    public void MapPaymentTransactionToSyncModel_ValidTransaction_ReturnsCorrectSyncModel()
    {
        // Arrange
        var transaction = PaymentRequestEntities.PaymentRequestToPerformOperation.Transactions.First();
        transaction.PublicTransactionNumber = "TXN456";

        // Act
        var result = _mapper.MapPaymentTransactionToSyncModel(transaction);

        // Assert
        result.ShouldNotBeNull();
        result.PaymentTransactionId.ShouldBe(transaction.Id.ToString());
        result.PublicTransactionNumber.ShouldBe("TXN456");
        result.ExecutedAt.ShouldBe(transaction.ExecutedAt);
    }
}
