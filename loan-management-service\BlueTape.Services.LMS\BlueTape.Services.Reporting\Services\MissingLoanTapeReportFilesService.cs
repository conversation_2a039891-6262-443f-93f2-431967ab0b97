using System.Globalization;
using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Constants;
using BlueTape.Services.Reporting.Domain.Enums;
using BlueTape.Services.Reporting.Domain.Options;
using BlueTape.Common.ExceptionHandling.Extensions;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using FundingSourceLMS = BlueTape.Services.LMS.Domain.Enums.FundingSource;

namespace BlueTape.Services.Reporting.Services;

public class MissingLoanTapeReportFilesService(
    IReportsUploadService reportsUploadService,
    ISlackNotificationService notificationService,
    ITraceIdAccessor traceIdAccessor,
    IDateProvider dateProvider,
    IOptions<MissingLoanTapeReportFilesOptions> options,
    ILogger<MissingLoanTapeReportFilesService> logger)
    : IMissingLoanTapeReportFilesService
{
    private readonly MissingLoanTapeReportFilesOptions _options = options.Value;

    private FundingSourceLMS[] GetConfiguredFundingSources()
    {
        return _options.FundingSources
            .Select(fs =>
                Enum.TryParse<FundingSourceLMS>(fs, true, out var fundingSource)
                    ? fundingSource
                    : (FundingSourceLMS?)null)
            .Where(fs => fs.HasValue)
            .Select(fs => fs!.Value)
            .ToArray();
    }

    public async Task CheckMissingReportFiles(CancellationToken ct)
    {
        logger.LogInformation("Starting check for missing loan tape report files");

        var today = dateProvider.CurrentDate;
        var missingFiles = new List<string>();

        // Check for the last X days
        for (int i = 0; i <= _options.DaysToCheck; i++)
        {
            var checkDate = today.AddDays(-i);
            var dateString = checkDate.ToString("yyyy-MM-dd");

            // Process different report types
            await CheckFundingSourceReports(missingFiles, dateString, ct);
        }

        // Notify if any files are missing
        if (missingFiles.Count > 0)
        {
            await NotifyMissingFiles(missingFiles, ct);
        }
        else
        {
            logger.LogInformation("No missing loan tape report files found");
        }
    }

    private async Task CheckFundingSourceReports(List<string> missingFiles, string dateString,
        CancellationToken ct)
    {
        var fundingSources = GetConfiguredFundingSources();

        foreach (var fundingSource in fundingSources)
        {
            foreach (var reportType in _options.ReportTypes)
            {
                if (ParseReportType(reportType, out var parsedReportType))
                {
                    await CheckReportByType(missingFiles, parsedReportType, fundingSource, dateString, ct);
                }
                else
                {
                    logger.LogWarning("Invalid report type in configuration: {ReportType}", reportType);
                }
            }
        }
    }

    private static bool ParseReportType(string reportType, out ReportType parsedReportType)
    {
        return Enum.TryParse( $"LoanTape{reportType}", true, out parsedReportType);
    }

    private async Task CheckReportByType(List<string> missingFiles, ReportType reportType,
        FundingSourceLMS fundingSource, string dateString, CancellationToken ct)
    {
        switch (reportType)
        {
            case ReportType.LoanTapeFactoring:
                await CheckAndAddMissingFile(missingFiles,
                    $"{LoanTapeReportConstants.FactoringSalesReportFileNamePrefix}_{fundingSource}_{dateString}.csv",
                    ct);
                break;

            case ReportType.LoanTapeAionLoanSales:
                await CheckAndAddMissingFile(missingFiles,
                    $"{LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix}_{fundingSource}_{dateString}.csv",
                    ct);
                break;

            case ReportType.LoanTapeFinalPayment:
                await CheckAndAddMissingFile(missingFiles,
                    $"{LoanTapeReportConstants.FinalPaymentReportFileNamePrefix}_{fundingSource}_{dateString}.csv", ct);
                break;

            default:
                logger.LogWarning("Unsupported report type: {ReportType}", reportType);
                break;
        }
    }


    private async Task CheckAndAddMissingFile(List<string> missingFiles, string fileName, CancellationToken ct)
    {
        var fileExists = await reportsUploadService.IsFileExistInS3Bucket(fileName, ct);
        if (!fileExists)
        {
            missingFiles.Add(fileName);
        }
    }

    private async Task NotifyMissingFiles(List<string> missingFiles, CancellationToken ct)
    {
        logger.LogWarning("Found {Count} missing loan tape report files", missingFiles.Count);

        var message = new StringBuilder();
        message.AppendLine("Missing Loan Tape Report Files Detected:");

        foreach (var file in missingFiles)
        {
            message.AppendLine($"- {file}");
        }

        var notificationBody = new EventMessageBody
        {
            Message = message.ToString(),
            EventLevel = EventLevel.Warning,
            EventName = LoanTapeReportConstants.MissingLoanTapeReportFilesJobName,
            EventSource = EnvironmentExtensions.GetExecutionEnvironment(),
            ServiceName = LoggerConstants.ProjectValue,
            TimeStamp = DateTime.UtcNow.ToString(CultureInfo.InvariantCulture),
            AwsAccountId = Environment.GetEnvironmentVariable("LP_AWS_ACCOUNT") ?? "Not provided in service"
        };

        await notificationService.Notify(notificationBody, traceIdAccessor.TraceId, ct);
    }
}