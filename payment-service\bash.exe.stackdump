Stack trace:
Frame         Function      Args
0007FFFFBF70  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE70) msys-2.0.dll+0x1FE8E
0007FFFFBF70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC248) msys-2.0.dll+0x67F9
0007FFFFBF70  000210046832 (000210286019, 0007FFFFBE28, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF70  000210068E24 (0007FFFFBF80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC250  00021006A225 (0007FFFFBF80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF04640000 ntdll.dll
7FFF03550000 KERNEL32.DLL
7FFF01DC0000 KERNELBASE.dll
7FFF03B70000 USER32.dll
7FFF018E0000 win32u.dll
7FFF03AB0000 GDI32.dll
7FFF01910000 gdi32full.dll
7FFF021B0000 msvcp_win.dll
7FFF01BD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF02F70000 advapi32.dll
7FFF024E0000 msvcrt.dll
7FFF03940000 sechost.dll
7FFF03040000 RPCRT4.dll
7FFF00CA0000 CRYPTBASE.DLL
7FFF01D20000 bcryptPrimitives.dll
7FFF03390000 IMM32.DLL
