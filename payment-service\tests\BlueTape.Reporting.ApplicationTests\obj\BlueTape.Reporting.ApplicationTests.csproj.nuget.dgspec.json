{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.Reporting.ApplicationTests\\BlueTape.Reporting.ApplicationTests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj", "projectName": "BlueTape.PaymentService.CompatibilityService", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.External\\BlueTape.PaymentService.DataAccess.External.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.External\\BlueTape.PaymentService.DataAccess.External.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.InvoiceService": {"target": "Package", "version": "[1.0.40, )"}, "BlueTape.InvoiceService.Common": {"target": "Package", "version": "[1.1.3, )"}, "BlueTape.LinqpalClient": {"target": "Package", "version": "[1.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.External\\BlueTape.PaymentService.DataAccess.External.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.External\\BlueTape.PaymentService.DataAccess.External.csproj", "projectName": "BlueTape.PaymentService.DataAccess.External", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.External\\BlueTape.PaymentService.DataAccess.External.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.External\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj", "projectName": "BlueTape.PaymentService.DataAccess.Mongo", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "BlueTape.MongoDB": {"target": "Package", "version": "[1.1.32, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj", "projectName": "BlueTape.PaymentService.DataAccess", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.AzureKeyVault": {"target": "Package", "version": "[1.0.3, )"}, "EntityFrameworkCore.Triggered": {"target": "Package", "version": "[3.2.2, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "TinyHelpers.EntityFrameworkCore": {"target": "Package", "version": "[3.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj", "projectName": "BlueTape.PaymentService.Domain", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.Common.ExceptionHandling": {"target": "Package", "version": "[1.0.8, )"}, "BlueTape.Integrations.Aion.Infrastructure": {"target": "Package", "version": "[1.0.20, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "bluetape.aionserviceclient": {"target": "Package", "version": "[1.0.33, )"}, "bluetape.loanserviceclient": {"target": "Package", "version": "[1.0.34, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj", "projectName": "BlueTape.PaymentService.PaymentFlowTemplatesEngine", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.AzureKeyVault": {"target": "Package", "version": "[1.0.3, )"}, "BlueTape.CompanyClient": {"target": "Package", "version": "[1.0.52, )"}, "BlueTape.Integrations.Aion.Infrastructure": {"target": "Package", "version": "[1.0.20, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.2, )"}, "TinyHelpers": {"target": "Package", "version": "[3.0.2, )"}, "bluetape.aionserviceclient": {"target": "Package", "version": "[1.0.33, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj", "projectName": "BlueTape.PaymentService.UnitOfWork", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess\\BlueTape.PaymentService.DataAccess.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Domain\\BlueTape.PaymentService.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj", "projectName": "BlueTape.Reporting.Application", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\NuGet.Config", "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.CompatibilityService\\BlueTape.PaymentService.CompatibilityService.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.DataAccess.Mongo\\BlueTape.PaymentService.DataAccess.Mongo.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.PaymentFlowTemplatesEngine\\BlueTape.PaymentService.PaymentFlowTemplatesEngine.csproj"}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.UnitOfWork\\BlueTape.PaymentService.UnitOfWork.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BlueTape.AWSS3": {"target": "Package", "version": "[1.1.6, )"}, "BlueTape.Common.FileService": {"target": "Package", "version": "[1.0.6, )"}, "BlueTape.EmailSender": {"target": "Package", "version": "[3.0.7, )"}, "BlueTape.Utilities": {"target": "Package", "version": "[1.4.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.Reporting.ApplicationTests\\BlueTape.Reporting.ApplicationTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.Reporting.ApplicationTests\\BlueTape.Reporting.ApplicationTests.csproj", "projectName": "BlueTape.Reporting.ApplicationTests", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.Reporting.ApplicationTests\\BlueTape.Reporting.ApplicationTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\tests\\BlueTape.Reporting.ApplicationTests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.Reporting.Application\\BlueTape.Reporting.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "NSubstitute": {"target": "Package", "version": "[5.1.0, )"}, "Shouldly": {"target": "Package", "version": "[4.2.1, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.3, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}