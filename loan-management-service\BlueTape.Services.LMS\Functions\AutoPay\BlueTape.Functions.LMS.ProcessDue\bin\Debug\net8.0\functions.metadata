[{"name": "ProcessDueConsumer", "scriptFile": "BlueTape.Functions.LMS.ProcessDue.dll", "entryPoint": "BlueTape.Functions.LMS.ProcessDue.ProcessDueConsumer.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "message", "direction": "In", "type": "serviceBusTrigger", "queueName": "%dueLoansProcessQueueName%", "connection": "dueLoansProcessQueueConnection", "cardinality": "One", "properties": {"supportsDeferredBinding": "True"}}]}]