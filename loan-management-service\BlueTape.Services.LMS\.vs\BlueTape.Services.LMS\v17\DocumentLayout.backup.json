{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\infrastructure\\changeloanstatusstrategy\\startloanstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\infrastructure\\changeloanstatusstrategy\\startloanstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\notificationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\autopayloancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\autopayloancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\authorizationperiodcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\authorizationperiodcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.ars.integrationtests\\entities\\loantemplates\\loantemplateentities.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|solutionrelative:bluetape.services.ars.integrationtests\\entities\\loantemplates\\loantemplateentities.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\createloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\calculatorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\calculatorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\loancontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\calculators\\datecalculator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\calculators\\datecalculator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{387B9567-5260-4169-9D7D-5653131DC7F5}|Functions\\BlueTape.Functions.LMS.PenaltyDetector\\BlueTape.Functions.LMS.PenaltyDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.penaltydetector\\penaltydetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{387B9567-5260-4169-9D7D-5653131DC7F5}|Functions\\BlueTape.Functions.LMS.PenaltyDetector\\BlueTape.Functions.LMS.PenaltyDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.penaltydetector\\penaltydetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\сreditstatusdetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\сreditstatusdetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\creditservices\\creditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\creditservices\\creditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusdetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusdetector.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\creditstatusconsumer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\creditcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\creditcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\abstractions\\services\\creditservices\\icreditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\abstractions\\services\\creditservices\\icreditstatusdetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\creditservices\\creditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\creditservices\\creditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\abstractions\\services\\creditservices\\icreditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\abstractions\\services\\creditservices\\icreditservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.dataaccess\\repositories\\loanrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|solutionrelative:bluetape.services.lms.dataaccess\\repositories\\loanrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\overduedetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\overduedetectorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\calculatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\calculatorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymenteligibilitychecker.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\qacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\qacontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\auxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\auxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.dataaccess\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C7F8D1BE-28DB-4E85-B0B7-592B7071A70C}|BlueTape.Services.LMS.DataAccess\\BlueTape.Services.LMS.DataAccess.csproj|solutionrelative:bluetape.services.lms.dataaccess\\di\\dependencyregistrar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\paymentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\paymentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\penaltyservices\\penaltydetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\penaltyservices\\penaltydetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\overduedetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\overduedetectorservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\loanparameterscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\loanparameterscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\creditholdscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\creditholdscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\basispointcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\basispointcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\overdueservices\\latefeereceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\overdueservices\\latefeereceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\abstractions\\services\\loanservices\\iauxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\abstractions\\services\\loanservices\\iauxiliaryloanservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanreceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanreceivableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanreceivablepaymenttimelineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanreceivablepaymenttimelineservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\overdueservices\\overduedetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\overdueservices\\overduedetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\penaltyservices\\penaltydetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\penaltyservices\\penaltydetectorservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\senders\\invoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\senders\\invoicesyncmessagesender.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5DA9CB35-7181-4B23-8DF7-7E351B973F9B}|BlueTape.Services.ARS.IntegrationTests\\BlueTape.Services.ARS.IntegrationTests.csproj|solutionrelative:bluetape.services.ars.integrationtests\\tests\\base\\arsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loanpayablesdetailsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loanpayablesdetailsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\loanservices\\loandownpaymentsservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\extensions\\loanextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\extensions\\loanextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\extensions\\loanextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\extensions\\loanextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\infrastructure\\changepaymentstatusstrategy\\approvepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\infrastructure\\changepaymentstatusstrategy\\approvepaymentstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\services\\paymentservices\\paymentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\services\\paymentservices\\paymentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\models\\loanreceivables\\loanreceivable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\models\\loanreceivables\\loanreceivable.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application.tests\\services\\createloanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C97825F1-C80F-4483-A156-E9A0EB418F50}|BlueTape.Services.LMS.Application.Tests\\BlueTape.Services.LMS.Application.Tests.csproj|solutionrelative:bluetape.services.lms.application.tests\\services\\createloanservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B75391C9-B056-450E-96E8-D3AE980AF7A6}|Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.overduedetector\\bluetape.functions.lms.overduedetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{B75391C9-B056-450E-96E8-D3AE980AF7A6}|Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.overduedetector\\bluetape.functions.lms.overduedetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\controllers\\agingreportscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.integrationtests\\tests\\base\\lmsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|solutionrelative:bluetape.services.lms.integrationtests\\tests\\base\\lmsintegrationtestsbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.integrationtests\\tests\\changereceivableintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{87C3D140-B18E-471A-9801-2BA0EEAD10BC}|BlueTape.Services.LMS.IntegrationTests\\BlueTape.Services.LMS.IntegrationTests.csproj|solutionrelative:bluetape.services.lms.integrationtests\\tests\\changereceivableintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\appsettings.beta.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{47EA21B1-3C61-4458-9AAB-FC66B8EB8AE6}|BlueTape.Services.LMS.API\\BlueTape.Services.LMS.API.csproj|solutionrelative:bluetape.services.lms.api\\middlewares\\exceptionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\functions\\bluetape.functions.lms.creditstatusdetector\\bluetape.functions.lms.creditstatusdetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{42A2F414-9839-4E50-BEE6-811F288E2932}|Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj|solutionrelative:functions\\bluetape.functions.lms.creditstatusdetector\\bluetape.functions.lms.creditstatusdetector.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|c:\\users\\<USER>\\source\\repos\\bluetape\\loan-management-service\\bluetape.services.lms\\bluetape.services.lms.application\\constants\\exceptionconstants.cs||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{0533DD8A-67A3-4B67-B55B-706736B3C199}|BlueTape.Services.LMS.Application\\BlueTape.Services.LMS.Application.csproj|solutionrelative:bluetape.services.lms.application\\constants\\exceptionconstants.cs||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 62, "Title": "ExceptionConstants.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Constants\\ExceptionConstants.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Constants\\ExceptionConstants.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Constants\\ExceptionConstants.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Constants\\ExceptionConstants.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T10:57:18.858Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "StartLoanStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Infrastructure\\ChangeLoanStatusStrategy\\StartLoanStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Infrastructure\\ChangeLoanStatusStrategy\\StartLoanStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Infrastructure\\ChangeLoanStatusStrategy\\StartLoanStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Infrastructure\\ChangeLoanStatusStrategy\\StartLoanStrategy.cs", "ViewState": "AgIAACIAAAAAAAAAAAAYwDgAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T10:55:45.04Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "NotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\NotificationService.cs", "ViewState": "AgIAANsAAAAAAAAAAAArwPMAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-14T08:53:03.386Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "PenaltyDetector.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.PenaltyDetector\\PenaltyDetector.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.PenaltyDetector\\PenaltyDetector.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.PenaltyDetector\\PenaltyDetector.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.PenaltyDetector\\PenaltyDetector.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T16:11:15.027Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "CreditController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CreditController.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:46.491Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "CreditStatusConsumer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusConsumer.cs", "ViewState": "AgIAAAgAAAAAAAAAAIA3wBUAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:06.704Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "LoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\LoanController.cs", "ViewState": "AgIAAL8AAAAAAAAAAAAuwOAAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T11:04:09.23Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "LoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanService.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAQwGwAAABOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:38:02.305Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "AuthorizationPeriodController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AuthorizationPeriodController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:52.214Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "AutopayLoanController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AutopayLoanController.cs", "ViewState": "AgIAACwAAAAAAAAAAAAgwEMAAABZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T11:04:12.866Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "CalculatorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CalculatorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CalculatorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CalculatorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CalculatorService.cs", "ViewState": "AgIAABwBAAAAAAAAAAAgwDABAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T16:14:17.611Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "DateCalculator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Calculators\\DateCalculator.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Calculators\\DateCalculator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Calculators\\DateCalculator.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Calculators\\DateCalculator.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAowBYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T16:17:32.889Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "LoanTemplateEntities.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Entities\\LoanTemplates\\LoanTemplateEntities.cs", "RelativeDocumentMoniker": "BlueTape.Services.ARS.IntegrationTests\\Entities\\LoanTemplates\\LoanTemplateEntities.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Entities\\LoanTemplates\\LoanTemplateEntities.cs", "RelativeToolTip": "BlueTape.Services.ARS.IntegrationTests\\Entities\\LoanTemplates\\LoanTemplateEntities.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAC3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T16:22:10.819Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "CreateLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\CreateLoanService.cs", "ViewState": "AgIAADMAAAAAAAAAAAAowFEAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:28:16.328Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "СreditStatusDetectorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\СreditStatusDetectorController.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwCAAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:52.393Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "CreditStatusDetector.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\CreditStatusDetector.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:14.795Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "IEmailNotificationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "RelativeDocumentMoniker": "..\\..\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "RelativeToolTip": "..\\..\\payment-service\\src\\BlueTape.PaymentService.Application\\Abstractions\\Services\\Notification\\IEmailNotificationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T15:01:04.633Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "CreditStatusDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditStatusDetectorService.cs", "ViewState": "AgIAABsAAAAAAAAAAAAlwCgAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T13:40:20.577Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "ICreditStatusDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditStatusDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditStatusDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditStatusDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditStatusDetectorService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T14:39:15.906Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "CreditService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\CreditServices\\CreditService.cs", "ViewState": "AgIAAKoAAAAAAAAAAAAQwLcAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T14:22:54.443Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\Properties\\launchSettings.json", "ViewState": "AgIAABUAAAAAAAAAAAAAwCIAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-08T11:51:13.71Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "ICreditService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\CreditServices\\ICreditService.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAYwBYAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T14:33:24.655Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "LoanRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "RelativeToolTip": "BlueTape.Services.LMS.DataAccess\\Repositories\\LoanRepository.cs", "ViewState": "AgIAANcAAAAAAAAAAAArwNcAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:38:09.789Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "CalculatorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CalculatorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:54.336Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAAI0AAAAAAAAAAAD4v6UAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:30:21.213Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "OverDueDetectorController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\OverDueDetectorController.cs", "ViewState": "AgIAABMAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T08:00:46.42Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "PaymentEligibilityChecker.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentEligibilityChecker.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAawA4AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:11:58.197Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "PaymentController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\PaymentController.cs", "ViewState": "AgIAAGcAAAAAAAAAAAAswH8AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T16:09:22.047Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "DependencyRegistrar.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "RelativeToolTip": "BlueTape.Services.LMS.DataAccess\\DI\\DependencyRegistrar.cs", "ViewState": "AgIAABEAAAAAAAAAAAAxwBwAAABhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T10:38:06.89Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Program.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Program.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAkwIMAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:28:22.227Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "PenaltyDetectorServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\PenaltyServices\\PenaltyDetectorServiceTests.cs", "ViewState": "AgIAAAUBAAAAAAAAAAAWwCcBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:29:33.565Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "AuxiliaryLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\AuxiliaryLoanService.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAkwEgAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:31:53.795Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "QaController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\QaController.cs", "ViewState": "AgIAAGUAAAAAAAAAAAAAAGsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T12:29:23.953Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "BasisPointController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\BasisPointController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:52.926Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "LoanParametersController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\LoanParametersController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:56.844Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "OverDueDetectorServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\OverDueDetectorServiceTests.cs", "ViewState": "AgIAAEAAAAAAAAAAAAD4v2AAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:29:05.155Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "CreditHoldsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\CreditHoldsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:18:47.365Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "IAuxiliaryLoanService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Abstractions\\Services\\LoanServices\\IAuxiliaryLoanService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:53:13.822Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "AdminController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AdminController.cs", "ViewState": "AgIAAAwDAAAAAAAAAAAcwBUDAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-30T11:27:47.591Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "LoanReceivableService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanReceivableService.cs", "ViewState": "AgIAABIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:02:36.51Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "LoanReceivablePaymentTimelineService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanReceivablePaymentTimelineService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:02:35.423Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "LateFeeReceivableService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\LateFeeReceivableService.cs", "ViewState": "AgIAAOUAAAAAAAAAAAAswAEBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:45:51.689Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "OverDueDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\OverDueServices\\OverDueDetectorService.cs", "ViewState": "AgIAAG0AAAAAAAAAAAD4v3AAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:51:16.44Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "PenaltyDetectorService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PenaltyServices\\PenaltyDetectorService.cs", "ViewState": "AgIAAFcAAAAAAAAAAAD4v2sAAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T11:02:02.838Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "InvoiceSyncMessageSender.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Senders\\InvoiceSyncMessageSender.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:43:04.269Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "LoanPayablesDetailsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanPayablesDetailsService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:42:41.941Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "ArsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.ARS.IntegrationTests\\Tests\\Base\\ArsIntegrationTestsBase.cs", "ViewState": "AgIAAEEAAAAAAAAAAAD4v1UAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:17:31.613Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "PaymentService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\PaymentServices\\PaymentService.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAlwD0AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T12:15:11.78Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "LoanReceivable.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Models\\LoanReceivables\\LoanReceivable.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T14:06:54.322Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "LoanExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Extensions\\LoanExtensions.cs", "ViewState": "AgIAAFsAAAAAAAAAAAAkwG8AAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T15:26:34.845Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "LoanDownPaymentsService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Services\\LoanServices\\LoanDownPaymentsService.cs", "ViewState": "AgIAANQAAAAAAAAAAAAAANgAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:33:21.075Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "ApprovePaymentStrategy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application\\Infrastructure\\ChangePaymentStatusStrategy\\ApprovePaymentStrategy.cs", "ViewState": "AgIAAFsAAAAAAAAAAAAjwHYAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T15:35:49.019Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "LoanExtensionsTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Extensions\\LoanExtensionsTests.cs", "ViewState": "AgIAAJcAAAAAAAAAAAAWwKcAAABkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T16:01:53.557Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "CreateLoanServiceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.Application.Tests\\Services\\CreateLoanServiceTests.cs", "ViewState": "AgIAACQBAAAAAAAAAAAYwDoBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T10:14:45.525Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "BlueTape.Functions.LMS.OverDueDetector.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.OverDueDetector\\BlueTape.Functions.LMS.OverDueDetector.csproj", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-03T16:00:27.008Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "AgingReportsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Controllers\\AgingReportsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T14:43:20.518Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "ExceptionMiddleware.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "RelativeToolTip": "BlueTape.Services.LMS.API\\Middlewares\\ExceptionMiddleware.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAABxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:27:24.296Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "LmsIntegrationTestsBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "RelativeToolTip": "BlueTape.Services.LMS.IntegrationTests\\Tests\\Base\\LmsIntegrationTestsBase.cs", "ViewState": "AgIAAEMAAAAAAAAAAAD4v2kAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T12:17:53.281Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "ChangeReceivableIntegrationTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "RelativeDocumentMoniker": "BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "RelativeToolTip": "BlueTape.Services.LMS.IntegrationTests\\Tests\\ChangeReceivableIntegrationTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T11:35:27.768Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "appsettings.beta.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.beta.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\appsettings.beta.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.beta.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\appsettings.beta.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T11:37:33.142Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.json", "RelativeDocumentMoniker": "BlueTape.Services.LMS.API\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\BlueTape.Services.LMS.API\\appsettings.json", "RelativeToolTip": "BlueTape.Services.LMS.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T11:37:00.494Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "main.tf", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf", "RelativeDocumentMoniker": "..\\infra\\modules\\mod-fapp\\main.tf", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\infra\\modules\\mod-fapp\\main.tf", "RelativeToolTip": "..\\infra\\modules\\mod-fapp\\main.tf", "ViewState": "AgIAAAYAAAAAAAAAACBjwCEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-02T11:16:02.925Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "BlueTape.Functions.LMS.CreditStatusDetector.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "RelativeDocumentMoniker": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "RelativeToolTip": "Functions\\BlueTape.Functions.LMS.CreditStatusDetector\\BlueTape.Functions.LMS.CreditStatusDetector.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-01T13:40:03.416Z"}]}]}]}