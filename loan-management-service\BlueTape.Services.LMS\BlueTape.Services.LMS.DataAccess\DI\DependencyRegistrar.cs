﻿using BlueTape.AzureKeyVault.Abstractions;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories.ChangeLog;
using BlueTape.Services.LMS.DataAccess.Constants;
using BlueTape.Services.LMS.DataAccess.Contexts;
using BlueTape.Services.LMS.DataAccess.Repositories;
using BlueTape.Services.LMS.DataAccess.Repositories.ChangeLog;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Utilities.Providers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace BlueTape.Services.LMS.DataAccess.DI
{
    public static class DependencyRegistrar
    {
        public static void AddDataAccessDependencies(this IServiceCollection services, IConfiguration config)
        {
            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);

            Action<DbContextOptionsBuilder> configureOptions = context =>
            {

                if (string.IsNullOrEmpty(env) || env.Equals(EnvironmentConstants.Development))
                {
                    context.UseNpgsql(config.GetConnectionString(DIConstants.LocalDbConnection));
                }
                else
                {
                    var connectionString = config.GetSection(DIConstants.AzureDbConnection).Value;

                    if (string.IsNullOrEmpty(connectionString))
                    {
                        var provider = services.BuildServiceProvider();
                        var keyVaultService = provider.GetRequiredService<IKeyVaultService>();
                        connectionString = keyVaultService.GetSecret(DIConstants.AzureDbConnection).GetAwaiter()
                            .GetResult();
                    }

                    context.UseNpgsql(connectionString);
                }
            };

            services.AddDbContext<DatabaseContext>(configureOptions);
            
            services.AddTransient<ILoanParametersRepository, LoanParametersRepository>();
            services.AddTransient<ILoanRepository, LoanRepository>();
            services.AddTransient<IPaymentProcessTemplateHistoryRepository, PaymentProcessTemplateHistoryRepository>();
            services.AddTransient<IAuthorizationPeriodsRepository, AuthorizationPeriodsRepository>();
            services.AddTransient<ILoanTemplateRepository, LoanTemplateRepository>();
            services.AddTransient<IBasisPointRepository, BasisPointRepository>();
            services.AddTransient<IPaymentRepository, PaymentRepository>();
            services.AddTransient<ILoanReceivableRepository, LoanReceivableRepository>();
            services.AddTransient<IPenaltyExclusionsRepository, PenaltyExclusionsRepository>();
            services.AddTransient<ICreditRepository, CreditRepository>();
            services.AddTransient<IGenericRepository<ChangeLogEntity>, GenericRepository<ChangeLogEntity>>();
            services.AddTransient<ICreditStatusHistoryRepository, CreditStatusHistoryRepository>();

            services.AddTransient<IPaymentProcessTemplateRepository, PaymentProcessTemplateRepository>();
            services.AddTransient(typeof(IChangeLoggedGenericRepository<>), typeof(ChangeLoggedGenericRepository<>));

            services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));

            services.AddScoped<IDateProvider, DateProvider>();
        }
    }
}