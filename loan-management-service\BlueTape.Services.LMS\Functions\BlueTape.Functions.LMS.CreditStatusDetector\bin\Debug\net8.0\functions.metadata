[{"name": "CreditMigrationFunction", "scriptFile": "BlueTape.Functions.LMS.CreditStatusDetector.dll", "entryPoint": "BlueTape.Functions.LMS.CreditStatusDetector.CreditMigrationFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 1 1 1 *", "properties": {}}]}, {"name": "CreditStatusConsumer", "scriptFile": "BlueTape.Functions.LMS.CreditStatusDetector.dll", "entryPoint": "BlueTape.Functions.LMS.CreditStatusDetector.CreditStatusConsumer.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "message", "direction": "In", "type": "serviceBusTrigger", "queueName": "%creditStatusQueueName%", "connection": "creditStatusQueueConnection", "cardinality": "One", "properties": {"supportsDeferredBinding": "True"}}]}, {"name": "CreditStatusDetectorFunction", "scriptFile": "BlueTape.Functions.LMS.CreditStatusDetector.dll", "entryPoint": "BlueTape.Functions.LMS.CreditStatusDetector.CreditStatusDetector.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 0 13 * * *", "properties": {}}]}]