using BlueTape.PaymentService.CompatibilityService.Models;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Operation;
using BlueTape.PaymentService.DataAccess.Mongo.Entities.Transaction;
using BlueTape.PaymentService.Domain.Entities;

namespace BlueTape.PaymentService.CompatibilityService.Abstractions.Service.SubscriptionFee;

public interface ISubscriptionFeeCompatibilityMapper
{
    OperationEntity MapFromPaymentRequestToSubscriptionFeeOperation(PaymentRequestEntity paymentRequest);
    Task<TransactionEntity> MapFromPaymentTransactionToSubscriptionFeeLegacyTransaction(PaymentRequestEntity paymentRequest, OperationEntity operation, CancellationToken cancellationToken);
    SyncTransactionModel MapPaymentTransactionToSyncModel(PaymentTransactionEntity transaction);
    Task<UpdateTransactionEntity> MapSyncModelToUpdateTransactionEntity(SyncTransactionModel syncTransactionModel, CancellationToken cancellationToken);
}
