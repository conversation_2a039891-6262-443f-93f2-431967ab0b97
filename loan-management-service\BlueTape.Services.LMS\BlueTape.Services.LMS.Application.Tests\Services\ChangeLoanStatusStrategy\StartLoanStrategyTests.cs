﻿using AutoFixture;
using BlueTape.Services.LMS.Application.Abstractions.Calculators;
using BlueTape.Services.LMS.Application.Abstractions.Services;
using BlueTape.Services.LMS.Application.Infrastructure.ChangeLoanStatusStrategy;
using BlueTape.Services.LMS.Application.Models.Loans;
using BlueTape.Services.LMS.Application.Tests.Attributes;
using BlueTape.Services.LMS.Application.Tests.Entities.Loans;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System;
using System.Threading.Tasks;
using Xunit;

namespace BlueTape.Services.LMS.Application.Tests.Services.ChangeLoanStatusStrategy;
public class StartLoanStrategyTests
{
    private readonly StartLoanStrategy _changeLoanStatusStrategy;
    private readonly Mock<ILoanRepository> _loanRepositoryMock = new();
    private readonly Mock<ILoanReceivableCalculator> _loanReceivableCalculatorMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();
    private readonly Mock<ILogger<StartLoanStrategy>> _logger = new();
    private readonly Mock<ILoanReceivableService> _loanReceivableServiceMock = new();
    private readonly Mock<ILoanTemplateRepository> _loanTemplateRepositoryMock = new();

    public StartLoanStrategyTests()
    {
        _changeLoanStatusStrategy = new StartLoanStrategy(_loanRepositoryMock.Object, _loanReceivableServiceMock.Object,
            _loanReceivableCalculatorMock.Object, _loanTemplateRepositoryMock.Object, _dateProviderMock.Object, _logger.Object);
    }

    [Fact]
    public void IsApplicable_ValidDataStart_ReturnsFalse()
    {
        const LoanStatus status = LoanStatus.Closed;

        var result = _changeLoanStatusStrategy.IsApplicable(status);

        result.ShouldBe(false);
    }

    [Fact]
    public void IsApplicable_ValidDataStart_ReturnsTrue()
    {
        const LoanStatus status = LoanStatus.Started;

        var result = _changeLoanStatusStrategy.IsApplicable(status);

        result.ShouldBe(true);
    }

    [Theory, AutoDataWithDateOnly]
    public Task ChangeLoanStatus_StartedValidLoan_ReturnsLoan(LoanEntity loanEntity)
    {
        loanEntity.Status = LoanStatus.Created;
        var changeLoanStatusModel = new ChangeLoanStatusModel()
        {
            Id = Guid.NewGuid(),
            Status = LoanStatus.Started
        };

        _loanTemplateRepositoryMock.Setup(x => x.GetById(loanEntity.ActiveLoanTemplateId.Value, default)).ReturnsAsync(new LoanTemplateEntity()
        {
            TotalDurationInDays = 30
        });
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(loanEntity);
        _loanRepositoryMock.Setup(x => x.Add(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(loanEntity);

        return _changeLoanStatusStrategy.ChangeStatus(changeLoanStatusModel, default).ShouldNotThrowAsync();
    }

    [Theory, AutoDataWithDateOnly]
    public async Task ChangeLoanStatus_RefinanceDateIsNull_StartDateIsCurrentDate(LoanEntity loanEntity)
    {
        loanEntity.Status = LoanStatus.Created;
        var changeLoanStatusModel = new ChangeLoanStatusModel()
        {
            Id = Guid.NewGuid(),
            Status = LoanStatus.Started
        };
        var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
        _loanTemplateRepositoryMock.Setup(x => x.GetById(loanEntity.ActiveLoanTemplateId.Value, default)).ReturnsAsync(new LoanTemplateEntity()
        {
            TotalDurationInDays = 30
        });
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(loanEntity);
        _loanRepositoryMock.Setup(x => x.Add(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(loanEntity);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        await _changeLoanStatusStrategy.ChangeStatus(changeLoanStatusModel, default);

        loanEntity.StartDate.ShouldBe(currentDate);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task ChangeLoanStatus_RefinanceDateEarlierThanNow_StartDateIsRefinanceDate(LoanEntity loanEntity)
    {
        loanEntity.Status = LoanStatus.Created;
        var changeLoanStatusModel = new ChangeLoanStatusModel()
        {
            Id = Guid.NewGuid(),
            Status = LoanStatus.Started,
            RefinanceDate = new DateOnly(2024, 05, 05)
        };
        var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
        _loanTemplateRepositoryMock.Setup(x => x.GetById(loanEntity.ActiveLoanTemplateId.Value, default)).ReturnsAsync(new LoanTemplateEntity()
        {
            TotalDurationInDays = 30
        });
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(loanEntity);
        _loanRepositoryMock.Setup(x => x.Add(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(loanEntity);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        await _changeLoanStatusStrategy.ChangeStatus(changeLoanStatusModel, default);

        loanEntity.StartDate.ShouldBe(changeLoanStatusModel.RefinanceDate.Value);
    }

    [Theory, AutoDataWithDateOnly]
    public async Task ChangeLoanStatus_RefinanceDateLaterThanNow_StartDateIsCurrentDate(LoanEntity loanEntity)
    {
        loanEntity.Status = LoanStatus.Created;
        var changeLoanStatusModel = new ChangeLoanStatusModel()
        {
            Id = Guid.NewGuid(),
            Status = LoanStatus.Started,
            RefinanceDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(1))
        };
        var currentDate = DateOnly.FromDateTime(DateTime.UtcNow);
        _loanTemplateRepositoryMock.Setup(x => x.GetById(loanEntity.ActiveLoanTemplateId.Value, default)).ReturnsAsync(new LoanTemplateEntity()
        {
            TotalDurationInDays = 30
        });
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(loanEntity);
        _loanRepositoryMock.Setup(x => x.Add(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(loanEntity);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        await _changeLoanStatusStrategy.ChangeStatus(changeLoanStatusModel, default);

        loanEntity.StartDate.ShouldBe(currentDate);
    }

    [Fact]
    public Task ChangeLoanStatus_StartedInvalidId__ThrowsVariableNullException()
    {
        var changeLoanStatusModel = new ChangeLoanStatusModel()
        {
            Id = Guid.NewGuid(),
            Status = LoanStatus.Started
        };

        var fixture = new Fixture();
        fixture.Customizations.Add(
            new RandomNumericSequenceGenerator(100, 10000));
        var entity = InvalidLoanEntities.NullLoanEntity;

        _loanRepositoryMock.Setup(x => x.Add(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(entity);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(entity);

        async Task MethodCallFunc() =>
            await _changeLoanStatusStrategy.ChangeStatus(changeLoanStatusModel, default);
        return Should.ThrowAsync<VariableNullException>(MethodCallFunc);
    }

    [Fact]
    public Task StartLoan_StartedInvalidLoanParameters_InvalidLoanParameters_ThrowsVariableNullException()
    {
        var changeLoanStatusModel = new ChangeLoanStatusModel()
        {
            Id = Guid.NewGuid(),
            Status = LoanStatus.Started
        };

        var fixture = new Fixture();
        fixture.Customizations.Add(
            new RandomNumericSequenceGenerator(100, 10000));
        var entity = InvalidLoanEntities.StartLoanLoanEntity;
        entity.Status = LoanStatus.Created;

        _loanRepositoryMock.Setup(x => x.Add(It.IsAny<LoanEntity>(), default, default)).ReturnsAsync(entity);
        _loanRepositoryMock.Setup(x => x.GetById(It.IsAny<Guid>(), default)).ReturnsAsync(entity);

        async Task MethodCallFunc() =>
            await _changeLoanStatusStrategy.ChangeStatus(changeLoanStatusModel, default);
        return Should.ThrowAsync<VariableNullException>(MethodCallFunc);
    }

}
