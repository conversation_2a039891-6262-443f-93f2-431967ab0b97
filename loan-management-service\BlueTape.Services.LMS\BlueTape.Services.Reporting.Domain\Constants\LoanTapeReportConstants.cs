﻿namespace BlueTape.Services.Reporting.Domain.Constants;

public static class LoanTapeReportConstants
{
    public const string LoanTapeReportProcessName = "LoanTapeReport";
    public const string MissingLoanTapeReportFilesJobName = "MissingLoanTapeReportFilesJob";
    public const string DebtInvestorParticipation = "100";
    public const string DebtInvestor = "Arcadia";
    public const string FactoringCreditPolicy = "ARAdvance v1.0";
    public const string AionCreditPolicy = "v4.0";
    public const string AionBankName = "Aion";
    public const string CbwBankName = "CBW";

    public const string CreditPolicyVersion = "v2.0";
    public const string OldCreditPolicyVersion = "v1.0";


    public const string DrawType = "BT Supplier";
    public const string AionFundingBankAccountKeyName = "AION-SPV-DISBURSEMENTS-ACCOUNT-NUMBER";
    public const string CbwFundingBankAccountKeyName = "CBW-SPV-DISBURSEMENTS-ACCOUNT-NUMBER";


    public const int MinimalDrawsCountForReturningCustomer = 2;
    public const int AcceptableFicoScore = 620;
    public const int MaxLoanPricingPackageRate = 100;
    public const int FinalPaymentDays = 3;

    public const string YesFlag = "Yes";
    public const string NoFlag = "No";

    public const string FactoringSalesReportFileNamePrefix = "FACTORING_SALES_REPORT";
    public const string AionLoanSalesReportFileNamePrefix = "AION_LOAN_SALES_REPORT";
    public const string CbwLoanSalesReportFileNamePrefix = "CBW_LOAN_SALES_REPORT";
    public const string FinalPaymentReportFileNamePrefix = "FINAL_PAYMENT_REPORT";
    public const string DrawsFeeReportFileNamePrefix = "DRAWS_FEE_REPORT";
    public const string ManualPaymentsReportFileNamePrefix = "MANUAL_PAYMENTS_REPORT";
    public const string RejectedAccountsApplicationsFileNamePrefix = "REJECTED_ACCOUNTS_APPLICATIONS_REPORT";
    public const string RejectedInvoiceDrawsFileNamePrefix = "REJECTED_DRAWS_INVOICES_REPORT";

    /// <summary>
    /// Lists reports filenames which should be saved into specific bucket named dwh-assets in prod
    /// </summary>
    public static readonly string[] DwhReportsNames =
        [
            DrawsFeeReportFileNamePrefix,
            ManualPaymentsReportFileNamePrefix,
            RejectedInvoiceDrawsFileNamePrefix,
            RejectedAccountsApplicationsFileNamePrefix
        ];

    public static readonly DateOnly EndCbwDate = new(2024, 11, 01);
}
