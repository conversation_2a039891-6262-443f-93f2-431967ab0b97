﻿using BlueTape.AWSS3.Extensions;
using BlueTape.Common.FileService;
using BlueTape.EmailSender.Extensions;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Builders.Reports;
using BlueTape.Services.Reporting.DataAccess.DI;
using BlueTape.Services.Reporting.Domain.Options;
using BlueTape.Services.Reporting.Mapping.Profiles;
using BlueTape.Services.Reporting.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace BlueTape.Services.Reporting.DI;

public static class DependencyRegistrar
{
    public static void AddReportingDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddReportingDataAccessDependencies();
        services.AddAutoMapper(typeof(ReportingMappingProfile).GetTypeInfo().Assembly);
        AddReportingServices(services, configuration);
        AddReportsBuilders(services);
        AddS3Clients(services);
        AddNotificationServices(services, configuration);
    }

    private static void AddReportingServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IFeesReportService, FeesReportService>();
        services.AddScoped<ILoanTapeReportService, LoanTapeReportService>();
        services.AddScoped<IFinalPaymentReportService, FinalPaymentReportService>();
        services.AddScoped<IFactoringLoanTapeReportService, FactoringLoanTapeReportService>();
        services.AddScoped<IAionTradeCreditLoanTapeReportService, AionTradeCreditLoanTapeReportService>();
        services.AddScoped<ICbwTradeCreditLoanTapeReportService, CbwTradeCreditLoanTapeReportService>();
        services.AddScoped<ILoanReportsHistoryService, LoanReportsHistoryService>();
        services.AddScoped<IReportsUploadService, ReportsUploadService>();
        services.AddScoped<IReportFileGenerationService, ReportFileGenerationService>();
        services.AddScoped<IManualPaymentsReportService, ManualPaymentsReportService>();
        services.AddScoped<IRejectedInvoiceDrawsReportsService, RejectedInvoiceDrawsReportService>();
        services.AddScoped<IRejectedInvoiceDrawsReportRowDataBuilder, RejectedInvoiceDrawsReportRowDataBuilder>();
        services.AddScoped<IRejectedAccountsApplicationsReportsService, RejectedAccountsApplicationsReportService>();
        services.AddScoped<IRejectedAccountsApplicationsReportRowDataBuilder, RejectedAccountsApplicationsReportRowDataBuilder>();
        services.AddScoped<ISlackNotificationService, SlackNotificationService>();
        services.AddScoped<IMissingLoanTapeReportFilesService, MissingLoanTapeReportFilesService>();
        services.Configure<LoanTapeReportOptions>(configuration.GetSection(nameof(LoanTapeReportOptions)));
        services.Configure<AccountApplicationsRejectReportOptions>(configuration.GetSection(nameof(AccountApplicationsRejectReportOptions)));
        services.Configure<InvoiceDrawsRejectReportOptions>(configuration.GetSection(nameof(InvoiceDrawsRejectReportOptions)));
        services.Configure<CashFlowBuilderOptions>(configuration.GetSection(nameof(CashFlowBuilderOptions)));
        services.Configure<MissingLoanTapeReportFilesOptions>(configuration.GetSection(nameof(MissingLoanTapeReportFilesOptions)));

        services.AddFileHandlingServices();
    }

    private static void AddReportsBuilders(IServiceCollection services)
    {
        services.AddScoped<IAccountAuthorizationDataBuilder, AccountAuthorizationDataBuilder>();
        services.AddScoped<IDraftDataBuilder, DraftDataBuilder>();
        services.AddScoped<ICashFlowDataBuilder, CashFlowDataBuilder>();
        services.AddScoped<IHeaderDataBuilder, HeaderDataBuilder>();
        services.AddScoped<ILoanTapeReportRowDataBuilder, LoanTapeReportRowDataBuilder>();
        services.AddScoped<IPaymentsDataBuilder, PaymentsDataBuilder>();
        services.AddScoped<IFinalPaymentReportRowDataBuilder, FinalPaymentReportRowDataBuilder>();
        services.AddScoped<IDrawFeesReportRowDataBuilder, DrawFeesReportRowDataBuilder>();
        services.AddScoped<IManualPaymentsReportRowDataBuilder, ManualPaymentsReportRowDataBuilder>();
    }

    private static void AddS3Clients(IServiceCollection services)
    {
        services.AddDynamicS3Client();
    }

    private static void AddNotificationServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddTransient<IReportsNotificationsService, ReportsNotificationsService>();
        services.AddEmailService(options =>
        {
            options.ApiKey = configuration["LP-SENDGRID-API-KEY"];
        });
    }
}
