{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET8_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "12.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v8.0": {"BlueTape.Services.ARS.IntegrationTests/1.0.0": {"dependencies": {"BlueTape.Services.LMS.API": "1.0.0", "BlueTape.Services.LMS.Application": "1.0.0", "Microsoft.AspNetCore.Mvc.Testing": "8.0.0", "Microsoft.EntityFrameworkCore.InMemory": "8.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.70", "Shouldly": "4.2.1", "coverlet.collector": "6.0.3", "xunit": "2.9.2", "xunit.runner.visualstudio": "3.0.0", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions": "*******", "Microsoft.AspNetCore.Authentication.BearerToken": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core": "*******", "Microsoft.AspNetCore.Authentication": "*******", "Microsoft.AspNetCore.Authentication.OAuth": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Endpoints": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions": "*******", "Microsoft.AspNetCore.DataProtection": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions": "*******", "Microsoft.AspNetCore.Diagnostics": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Hosting.Reference": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http.Reference": "*******", "Microsoft.AspNetCore.Http.Extensions.Reference": "*******", "Microsoft.AspNetCore.Http.Features.Reference": "*******", "Microsoft.AspNetCore.Http.Results": "*******", "Microsoft.AspNetCore.HttpLogging": "*******", "Microsoft.AspNetCore.HttpOverrides": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.OutputCaching": "*******", "Microsoft.AspNetCore.RateLimiting": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.RequestDecompression": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions": "*******", "Microsoft.AspNetCore.Routing": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core": "*******", "Microsoft.AspNetCore.Server.Kestrel": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities.Reference": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Caching.Abstractions.Reference": "*******", "Microsoft.Extensions.Caching.Memory.Reference": "*******", "Microsoft.Extensions.Configuration.Abstractions.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine.Reference": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "*******", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json.Reference": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "*******", "Microsoft.Extensions.DependencyInjection.Reference": "*******", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "*******", "Microsoft.Extensions.Diagnostics.Reference": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.Features": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting.Reference": "*******", "Microsoft.Extensions.Http.Reference": "*******", "Microsoft.Extensions.Identity.Core": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Abstractions.Reference": "*******", "Microsoft.Extensions.Logging.Configuration.Reference": "*******", "Microsoft.Extensions.Logging.Console.Reference": "*******", "Microsoft.Extensions.Logging.Debug.Reference": "*******", "Microsoft.Extensions.Logging.Reference": "*******", "Microsoft.Extensions.Logging.EventLog.Reference": "*******", "Microsoft.Extensions.Logging.EventSource.Reference": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.Options.Reference": "*******", "Microsoft.Extensions.Primitives.Reference": "*******", "Microsoft.Extensions.WebEncoders": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "1*******", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives.Reference": "*******", "Microsoft.Win32.Registry.Reference": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext.Reference": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent.Reference": "*******", "System.Collections.Reference": "*******", "System.Collections.Immutable.Reference": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.Annotations": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console.Reference": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug.Reference": "*******", "System.Diagnostics.DiagnosticSource.Reference": "*******", "System.Diagnostics.EventLog.Reference": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools.Reference": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing.Reference": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Formats.Asn1": "*******", "System.Formats.Tar": "*******", "System.Globalization.Calendars.Reference": "*******", "System.Globalization.Reference": "*******", "System.Globalization.Extensions.Reference": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression.Reference": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile.Reference": "*******", "System.IO.Reference": "*******", "System.IO.FileSystem.AccessControl.Reference": "*******", "System.IO.FileSystem.Reference": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives.Reference": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines.Reference": "*******", "System.IO.Pipes.AccessControl": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq.Reference": "*******", "System.Linq.Expressions.Reference": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http.Reference": "*******", "System.Net.Http.Json": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives.Reference": "*******", "System.Net.Quic": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets.Reference": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors.Reference": "*******", "System.ObjectModel.Reference": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection.Reference": "*******", "System.Reflection.Emit.Reference": "*******", "System.Reflection.Emit.ILGeneration.Reference": "*******", "System.Reflection.Emit.Lightweight.Reference": "*******", "System.Reflection.Extensions.Reference": "*******", "System.Reflection.Metadata.Reference": "*******", "System.Reflection.Primitives.Reference": "*******", "System.Reflection.TypeExtensions.Reference": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager.Reference": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.Unsafe.Reference": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime.Reference": "*******", "System.Runtime.Extensions.Reference": "*******", "System.Runtime.Handles.Reference": "*******", "System.Runtime.InteropServices.Reference": "*******", "System.Runtime.InteropServices.JavaScript": "*******", "System.Runtime.InteropServices.RuntimeInformation.Reference": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics.Reference": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "*******", "System.Security.AccessControl.Reference": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms.Reference": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp.Reference": "*******", "System.Security.Cryptography": "*******", "System.Security.Cryptography.Encoding.Reference": "*******", "System.Security.Cryptography.OpenSsl.Reference": "*******", "System.Security.Cryptography.Primitives.Reference": "*******", "System.Security.Cryptography.X509Certificates.Reference": "*******", "System.Security.Cryptography.Xml": "*******", "System.Security": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding.Reference": "*******", "System.Text.Encoding.Extensions.Reference": "*******", "System.Text.Encodings.Web.Reference": "*******", "System.Text.Json.Reference": "*******", "System.Text.RegularExpressions.Reference": "*******", "System.Threading.Channels": "*******", "System.Threading.Reference": "*******", "System.Threading.Overlapped": "*******", "System.Threading.RateLimiting": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks.Reference": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer.Reference": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter.Reference": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument.Reference": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"BlueTape.Services.ARS.IntegrationTests.dll": {}}, "compile": {"BlueTape.Services.ARS.IntegrationTests.dll": {}}}, "Audit.EntityFramework.Core/27.1.1": {"dependencies": {"Audit.NET": "27.1.1", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "runtime": {"lib/net8.0/Audit.EntityFramework.Core.dll": {"assemblyVersion": "27.1.1.0", "fileVersion": "27.1.1.0"}}, "compile": {"lib/net8.0/Audit.EntityFramework.Core.dll": {}}}, "Audit.NET/27.1.1": {"runtime": {"lib/net8.0/Audit.NET.dll": {"assemblyVersion": "27.1.1.0", "fileVersion": "27.1.1.0"}}, "compile": {"lib/net8.0/Audit.NET.dll": {}}}, "AutoFilter.Sql/2.0.0": {"runtime": {"lib/netstandard2.0/AutoFilter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/AutoFilter.dll": {}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}, "compile": {"lib/netstandard2.1/AutoMapper.dll": {}}}, "AutoMapper.Extensions.EnumMapping/3.1.0": {"dependencies": {"AutoMapper": "12.0.1"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.EnumMapping.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.0.0"}}, "compile": {"lib/netstandard2.1/AutoMapper.Extensions.EnumMapping.dll": {}}}, "AutoMapper.Extensions.ExpressionMapping/6.0.4": {"dependencies": {"AutoMapper": "12.0.1"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.ExpressionMapping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.4.0"}}, "compile": {"lib/netstandard2.1/AutoMapper.Extensions.ExpressionMapping.dll": {}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}, "compile": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {}}}, "AWSSDK.Core/3.7.302.6": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.302.6"}}, "compile": {"lib/net8.0/AWSSDK.Core.dll": {}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.2": {"dependencies": {"AWSSDK.Core": "3.7.302.6", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.1.0"}}, "compile": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {}}}, "AWSSDK.KeyManagementService/3.7.300.46": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.KeyManagementService.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.46"}}, "compile": {"lib/net8.0/AWSSDK.KeyManagementService.dll": {}}}, "AWSSDK.S3/3.7.10": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.10.0"}}, "compile": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {}}}, "AWSSDK.SecretsManager/3.7.302.21": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecretsManager.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.302.21"}}, "compile": {"lib/net8.0/AWSSDK.SecretsManager.dll": {}}}, "AWSSDK.SecretsManager.Caching/1.0.6": {"dependencies": {"AWSSDK.SecretsManager": "3.7.302.21", "Microsoft.Extensions.Caching.Memory": "8.0.0"}, "runtime": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {}}}, "AWSSDK.SecurityToken/3.7.300.47": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.300.47"}}, "compile": {"lib/net8.0/AWSSDK.SecurityToken.dll": {}}}, "AWSSDK.SimpleNotificationService/**********": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "**********"}}, "compile": {"lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll": {}}}, "Azure.Core/1.42.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.42.0.0", "fileVersion": "1.4200.24.40104"}}, "compile": {"lib/net6.0/Azure.Core.dll": {}}}, "Azure.Core.Amqp/1.3.0": {"dependencies": {"Microsoft.Azure.Amqp": "2.6.4", "System.Memory": "4.5.5", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.300.23.15207"}}, "compile": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {}}}, "Azure.Extensions.AspNetCore.Configuration.Secrets/1.3.2": {"dependencies": {"Azure.Core": "1.42.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Extensions.Configuration": "8.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Extensions.AspNetCore.Configuration.Secrets.dll": {"assemblyVersion": "1.3.2.0", "fileVersion": "1.300.224.41501"}}, "compile": {"lib/netstandard2.0/Azure.Extensions.AspNetCore.Configuration.Secrets.dll": {}}}, "Azure.Identity/1.10.4": {"dependencies": {"Azure.Core": "1.42.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.5", "System.Security.Cryptography.ProtectedData": "6.0.0", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.10.4.0", "fileVersion": "1.1000.423.56303"}}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {}}}, "Azure.Messaging.ServiceBus/7.17.1": {"dependencies": {"Azure.Core": "1.42.0", "Azure.Core.Amqp": "1.3.0", "Microsoft.Azure.Amqp": "2.6.4", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {"assemblyVersion": "7.17.1.0", "fileVersion": "7.1700.123.60403"}}, "compile": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {}}}, "Azure.Security.KeyVault.Keys/4.5.0": {"dependencies": {"Azure.Core": "1.42.0", "System.Memory": "4.5.5", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {}}}, "Azure.Security.KeyVault.Secrets/4.6.0": {"dependencies": {"Azure.Core": "1.42.0", "System.Memory": "4.5.5", "System.Text.Json": "8.0.4", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.6.0.0", "fileVersion": "4.600.24.11403"}}, "compile": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {}}}, "BlueTape.AWSS3/1.1.6": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.2", "AWSSDK.S3": "3.7.10", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.AWSS3.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}, "compile": {"lib/net6.0/BlueTape.AWSS3.dll": {}}}, "BlueTape.AzureKeyVault/1.0.3": {"dependencies": {"Azure.Identity": "1.10.4", "Azure.Security.KeyVault.Keys": "4.5.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}, "compile": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {}}}, "BlueTape.Common.ExceptionHandling/1.0.8": {"dependencies": {"BlueTape.SNS": "1.0.2"}, "runtime": {"lib/net6.0/BlueTape.Common.ExceptionHandling.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}, "compile": {"lib/net6.0/BlueTape.Common.ExceptionHandling.dll": {}}}, "BlueTape.Common.Extensions/1.1.0": {"runtime": {"lib/net6.0/BlueTape.Common.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/BlueTape.Common.Extensions.dll": {}}}, "BlueTape.Common.FileService/1.0.6": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "ClosedXML": "0.102.3", "CsvHelper": "32.0.3"}, "runtime": {"lib/net6.0/BlueTape.Common.FileService.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}, "compile": {"lib/net6.0/BlueTape.Common.FileService.dll": {}}}, "BlueTape.Common.Validation/1.0.4": {"dependencies": {"BlueTape.AWSS3": "1.1.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "libphonenumber-csharp": "8.12.45"}, "runtime": {"lib/net6.0/BlueTape.Common.Validation.dll": {"assemblyVersion": "1.0.4.0", "fileVersion": "1.0.4.0"}}, "compile": {"lib/net6.0/BlueTape.Common.Validation.dll": {}}}, "BlueTape.CompanyClient/1.0.53": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.CompanyService": "1.3.4", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.CompanyClient.dll": {"assemblyVersion": "1.0.53.0", "fileVersion": "1.0.53.0"}}, "compile": {"lib/net6.0/BlueTape.CompanyClient.dll": {}}}, "BlueTape.CompanyService/1.3.4": {"dependencies": {"BlueTape.CompanyService.Common": "1.1.21", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/BlueTape.CompanyService.dll": {"assemblyVersion": "1.3.4.0", "fileVersion": "1.3.4.0"}}, "compile": {"lib/net6.0/BlueTape.CompanyService.dll": {}}}, "BlueTape.CompanyService.Common/1.1.21": {"dependencies": {"BlueTape.LS": "1.1.78", "BlueTape.ServiceBusMessaging": "1.0.9"}, "runtime": {"lib/net6.0/BlueTape.CompanyService.Common.dll": {"assemblyVersion": "1.1.21.0", "fileVersion": "1.1.21.0"}}, "compile": {"lib/net6.0/BlueTape.CompanyService.Common.dll": {}}}, "BlueTape.DraftsMapper/1.0.1": {"dependencies": {"BlueTape.OBS": "1.6.72", "BlueTape.Utilities": "1.4.6"}, "runtime": {"lib/net6.0/BlueTape.DraftsMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/BlueTape.DraftsMapper.dll": {}}}, "BlueTape.EmailSender/3.0.7": {"dependencies": {"BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "SendGrid": "9.28.1", "SendGrid.Extensions.DependencyInjection": "1.0.1"}, "runtime": {"lib/net6.0/BlueTape.EmailSender.dll": {"assemblyVersion": "3.0.7.0", "fileVersion": "3.0.7.0"}}, "compile": {"lib/net6.0/BlueTape.EmailSender.dll": {}}}, "BlueTape.InvoiceClient/1.0.21": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.InvoiceService": "1.0.40", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.InvoiceClient.dll": {"assemblyVersion": "1.0.21.0", "fileVersion": "1.0.21.0"}}, "compile": {"lib/net6.0/BlueTape.InvoiceClient.dll": {}}}, "BlueTape.InvoiceService/1.0.40": {"dependencies": {"BlueTape.Common.Validation": "1.0.4", "BlueTape.InvoiceService.Common": "1.1.3", "BlueTape.OBS": "1.6.72", "BlueTape.PaymentService": "1.0.15", "FluentValidation": "11.9.0"}, "runtime": {"lib/net6.0/BlueTape.InvoiceService.dll": {"assemblyVersion": "1.0.40.0", "fileVersion": "1.0.40.0"}}, "compile": {"lib/net6.0/BlueTape.InvoiceService.dll": {}}}, "BlueTape.InvoiceService.Common/1.1.3": {"runtime": {"lib/net6.0/BlueTape.InvoiceService.Common.dll": {"assemblyVersion": "1.1.3.0", "fileVersion": "1.1.3.0"}}, "compile": {"lib/net6.0/BlueTape.InvoiceService.Common.dll": {}}}, "BlueTape.LinqpalClient/1.0.8": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.Utilities": "1.4.6", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/net6.0/BlueTape.LinqpalClient.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}, "compile": {"lib/net6.0/BlueTape.LinqpalClient.dll": {}}}, "BlueTape.LMS/1.0.2": {"dependencies": {"BlueTape.ServiceBusMessaging": "1.0.9"}, "runtime": {"lib/net6.0/BlueTape.LMS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/BlueTape.LMS.dll": {}}}, "BlueTape.LS/1.1.78": {"dependencies": {"BlueTape.LS.Domain": "1.1.36"}, "runtime": {"lib/net6.0/BlueTape.LS.dll": {"assemblyVersion": "1.1.78.0", "fileVersion": "1.1.78.0"}}, "compile": {"lib/net6.0/BlueTape.LS.dll": {}}}, "BlueTape.LS.Domain/1.1.36": {"dependencies": {"BlueTape.Utilities": "1.4.6"}, "runtime": {"lib/net6.0/BlueTape.LS.Domain.dll": {"assemblyVersion": "1.1.36.0", "fileVersion": "1.1.36.0"}}, "compile": {"lib/net6.0/BlueTape.LS.Domain.dll": {}}}, "BlueTape.MongoDB/1.1.32": {"dependencies": {"BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Utilities": "1.4.6", "MongoDB.Driver": "2.25.0"}, "runtime": {"lib/net6.0/BlueTape.MongoDB.dll": {"assemblyVersion": "1.1.6.0", "fileVersion": "1.1.6.0"}}, "compile": {"lib/net6.0/BlueTape.MongoDB.dll": {}}}, "BlueTape.Notification.Sender/1.0.5": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.SNS": "1.0.2", "BlueTape.ServiceBusMessaging": "1.0.9"}, "runtime": {"lib/net6.0/BlueTape.Notification.Sender.dll": {"assemblyVersion": "1.0.5.0", "fileVersion": "1.0.5.0"}}, "compile": {"lib/net6.0/BlueTape.Notification.Sender.dll": {}}}, "BlueTape.OBS/1.6.72": {"runtime": {"lib/net6.0/BlueTape.OBS.dll": {"assemblyVersion": "1.6.72.0", "fileVersion": "1.6.72.0"}}, "compile": {"lib/net6.0/BlueTape.OBS.dll": {}}}, "BlueTape.PaymentService/1.0.15": {"dependencies": {"BlueTape.ServiceBusMessaging": "1.0.9", "BlueTape.Utilities": "1.4.6"}, "runtime": {"lib/net6.0/BlueTape.PaymentService.dll": {"assemblyVersion": "1.0.15.0", "fileVersion": "1.0.15.0"}}, "compile": {"lib/net6.0/BlueTape.PaymentService.dll": {}}}, "BlueTape.ServiceBusMessaging/1.0.9": {"dependencies": {"Azure.Messaging.ServiceBus": "7.17.1", "BlueTape.AzureKeyVault": "1.0.3"}, "runtime": {"lib/net6.0/BlueTape.ServiceBusMessaging.dll": {"assemblyVersion": "1.0.9.0", "fileVersion": "1.0.9.0"}}, "compile": {"lib/net6.0/BlueTape.ServiceBusMessaging.dll": {}}}, "BlueTape.SNS/1.0.2": {"dependencies": {"AWSSDK.SimpleNotificationService": "**********", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Serilog.AspNetCore": "6.0.1"}, "runtime": {"lib/net6.0/BlueTape.SNS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/BlueTape.SNS.dll": {}}}, "BlueTape.Utilities/1.4.6": {"dependencies": {"AWSSDK.KeyManagementService": "3.7.300.46", "AWSSDK.SecretsManager": "3.7.302.21", "AWSSDK.SecretsManager.Caching": "1.0.6", "AWSSDK.SecurityToken": "3.7.300.47", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.OBS": "1.6.72", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Http.Polly": "6.0.9", "Microsoft.Extensions.Options": "8.0.2", "MongoDB.Bson": "2.25.0", "Polly": "7.2.3", "Serilog": "4.0.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.GlobalLogContext": "3.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Logz.Io": "7.1.0"}, "runtime": {"lib/net6.0/BlueTape.Utilities.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/BlueTape.Utilities.dll": {}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Castle.Core.dll": {}}}, "ClosedXML/0.102.3": {"dependencies": {"DocumentFormat.OpenXml": "2.16.0", "ExcelNumberFormat": "1.1.0", "SixLabors.Fonts": "1.0.0", "System.IO.Packaging": "6.0.0", "System.Net.Http": "4.3.4", "System.Text.RegularExpressions": "4.3.1", "XLParser": "1.5.2"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "0.102.3.0", "fileVersion": "0.102.3.0"}}, "compile": {"lib/netstandard2.1/ClosedXML.dll": {}}}, "coverlet.collector/6.0.3": {}, "CsvHelper/32.0.3": {"runtime": {"lib/net8.0/CsvHelper.dll": {"assemblyVersion": "3*******", "fileVersion": "32.0.3.22"}}, "compile": {"lib/net8.0/CsvHelper.dll": {}}}, "DateOnlyTimeOnly.AspNet/2.1.1": {"runtime": {"lib/net7.0/DateOnlyTimeOnly.AspNet.dll": {"assemblyVersion": "2.1.1.0", "fileVersion": "2.1.1.0"}}, "compile": {"lib/net7.0/DateOnlyTimeOnly.AspNet.dll": {}}}, "DateOnlyTimeOnly.AspNet.Swashbuckle/2.1.1": {"dependencies": {"DateOnlyTimeOnly.AspNet": "2.1.1", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.1"}, "runtime": {"lib/net7.0/DateOnlyTimeOnly.AspNet.Swashbuckle.dll": {"assemblyVersion": "2.1.1.0", "fileVersion": "2.1.1.0"}}, "compile": {"lib/net7.0/DateOnlyTimeOnly.AspNet.Swashbuckle.dll": {}}}, "DiffEngine/11.3.0": {"dependencies": {"EmptyFiles": "4.4.0", "System.Management": "6.0.1"}, "runtime": {"lib/net7.0/DiffEngine.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/DiffEngine.dll": {}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}, "compile": {"lib/net5.0/DnsClient.dll": {}}}, "DocumentFormat.OpenXml/2.16.0": {"dependencies": {"System.IO.Packaging": "6.0.0"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.16.0.0", "fileVersion": "2.16.0.0"}}, "compile": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {}}}, "Elastic.CommonSchema/1.5.3": {"dependencies": {"System.Text.Json": "8.0.4"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {"assemblyVersion": "*******", "fileVersion": "1.5.3.0"}}, "compile": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {}}}, "Elastic.CommonSchema.Serilog/1.5.3": {"dependencies": {"Elastic.CommonSchema": "1.5.3", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Serilog": "4.0.0"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "1.5.3.0"}}, "compile": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {}}}, "EmptyFiles/4.4.0": {"runtime": {"lib/net7.0/EmptyFiles.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/EmptyFiles.dll": {}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/ExcelNumberFormat.dll": {}}}, "FluentValidation/11.9.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.0.0"}}, "compile": {"lib/net8.0/FluentValidation.dll": {}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.9.0", "FluentValidation.DependencyInjectionExtensions": "11.9.0"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.3.0.0"}}, "compile": {"lib/net6.0/FluentValidation.AspNetCore.dll": {}}}, "FluentValidation.DependencyInjectionExtensions/11.9.0": {"dependencies": {"FluentValidation": "11.9.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "11.9.0.0"}}, "compile": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {}}}, "Irony.NetCore/1.0.11": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Reflection": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Numerics": "4.3.0"}, "runtime": {"lib/netstandard1.6/Irony.dll": {"assemblyVersion": "1.0.11.0", "fileVersion": "1.0.11.0"}}, "compile": {"lib/netstandard1.6/Irony.dll": {}}}, "libphonenumber-csharp/8.12.45": {"dependencies": {"System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.0/PhoneNumbers.dll": {"assemblyVersion": "8.12.45.0", "fileVersion": "8.12.45.0"}}, "compile": {"lib/netstandard2.0/PhoneNumbers.dll": {}}}, "Macross.Json.Extensions/3.0.0": {"runtime": {"lib/net6.0/Macross.Json.Extensions.dll": {"assemblyVersion": "3.0.0.22164", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Macross.Json.Extensions.dll": {}}}, "Microsoft.ApplicationInsights/2.22.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {}}}, "Microsoft.ApplicationInsights.AspNetCore/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.22", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.AspNetCore.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.AspNetCore.dll": {}}}, "Microsoft.ApplicationInsights.DependencyCollector/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.DependencyCollector.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.AI.DependencyCollector.dll": {}}}, "Microsoft.ApplicationInsights.EventCounterCollector/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.EventCounterCollector.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.AI.EventCounterCollector.dll": {}}}, "Microsoft.ApplicationInsights.PerfCounterCollector/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.PerfCounterCollector.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.AI.PerfCounterCollector.dll": {}}}, "Microsoft.ApplicationInsights.WindowsServer/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.WindowsServer.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.AI.WindowsServer.dll": {}}}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "System.IO.FileSystem.AccessControl": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AI.ServerTelemetryChannel.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.AI.ServerTelemetryChannel.dll": {}}}, "Microsoft.ApplicationInsights.WorkerService/2.21.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.22.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer": "2.22.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.22.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.22.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.WorkerService.dll": {"assemblyVersion": "2.21.0.429", "fileVersion": "2.21.0.429"}}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.WorkerService.dll": {}}}, "Microsoft.AspNetCore.Hosting/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.22", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.AspNetCore.Http/2.1.22": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.1.1", "Microsoft.Extensions.ObjectPool": "2.1.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Net.Http.Headers": "2.1.1"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Net.Http.Headers": "2.1.1", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.AspNetCore.JsonPatch/8.0.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.0", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {}}}, "Microsoft.AspNetCore.Mvc.Testing/8.0.0": {"dependencies": {"Microsoft.AspNetCore.TestHost": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.1", "Microsoft.Extensions.Hosting": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Testing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Testing.dll": {}}}, "Microsoft.AspNetCore.TestHost/8.0.0": {"dependencies": {"System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.TestHost.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.TestHost.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"dependencies": {"Microsoft.Net.Http.Headers": "2.1.1", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.Azure.Amqp/2.6.4": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {}}}, "Microsoft.Azure.Functions.Worker.ApplicationInsights/1.1.0": {"dependencies": {"Microsoft.ApplicationInsights.PerfCounterCollector": "2.22.0", "Microsoft.Azure.Functions.Worker.Core": "1.15.0", "Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.ApplicationInsights.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.Azure.Functions.Worker.ApplicationInsights.dll": {}}}, "Microsoft.Azure.Functions.Worker.Core/1.15.0": {"dependencies": {"Azure.Core": "1.42.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "System.Collections.Immutable": "5.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net5.0/Microsoft.Azure.Functions.Worker.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net5.0/Microsoft.Azure.Functions.Worker.Core.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.CodeCoverage/17.8.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.800.623.45702"}}, "compile": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.InMemory/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/8.0.1": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.724.31311"}}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Http.Polly/6.0.9": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41926"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.ApplicationInsights/2.22.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.ApplicationInsights.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.4"}}, "Microsoft.Extensions.ObjectPool/2.1.1": {}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Client/4.56.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.22.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}, "compile": {"lib/net6.0/Microsoft.Identity.Client.dll": {}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}, "compile": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {}}}, "Microsoft.IdentityModel.Abstractions/6.22.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.22.0.30727"}}, "compile": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.Net.Http.Headers/2.1.1": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.1"}}, "Microsoft.NET.Test.Sdk/17.8.0": {"dependencies": {"Microsoft.CodeCoverage": "17.8.0", "Microsoft.TestPlatform.TestHost": "17.8.0"}}, "Microsoft.NETCore.Platforms/1.1.1": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.TestPlatform.ObjectModel/17.8.0": {"dependencies": {"NuGet.Frameworks": "6.5.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}}, "Microsoft.TestPlatform.TestHost/17.8.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.8.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.800.23.55801"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {}}}, "MongoDB.Bson/2.25.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.1/MongoDB.Bson.dll": {}}}, "MongoDB.Driver/2.25.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Driver.Core": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.1/MongoDB.Driver.dll": {}}}, "MongoDB.Driver.Core/2.25.0": {"dependencies": {"AWSSDK.SecurityToken": "3.7.300.47", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {}}}, "MongoDB.Libmongocrypt/1.8.2": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.8.2.0", "fileVersion": "1.8.2.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {}}}, "Moq/4.20.70": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Moq.dll": {"assemblyVersion": "4.20.70.0", "fileVersion": "4.20.70.0"}}, "compile": {"lib/net6.0/Moq.dll": {}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}, "compile": {"lib/net6.0/Newtonsoft.Json.dll": {}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {}}}, "Npgsql/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Npgsql.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Npgsql": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {}}}, "NuGet.Frameworks/6.5.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "6.5.0.154", "fileVersion": "6.5.0.154"}}, "compile": {"lib/netstandard2.0/NuGet.Frameworks.dll": {}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.3.0"}}, "compile": {"lib/netstandard2.0/Polly.dll": {}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.3"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {}}}, "Quartz/3.8.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/Quartz.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Quartz.dll": {}}}, "Quartz.Extensions.DependencyInjection/3.8.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Quartz": "3.8.0"}, "runtime": {"lib/net8.0/Quartz.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Quartz.Extensions.DependencyInjection.dll": {}}}, "Quartz.Extensions.Hosting/3.8.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Quartz.Extensions.DependencyInjection": "3.8.0"}, "runtime": {"lib/net8.0/Quartz.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Quartz.Extensions.Hosting.dll": {}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SendGrid/9.28.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "starkbank-ecdsa": "1.3.3"}, "runtime": {"lib/netstandard2.0/SendGrid.dll": {"assemblyVersion": "9.28.1.0", "fileVersion": "9.28.1.0"}}, "compile": {"lib/netstandard2.0/SendGrid.dll": {}}}, "SendGrid.Extensions.DependencyInjection/1.0.1": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "SendGrid": "9.28.1"}, "runtime": {"lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.dll": {}}}, "Serilog/4.0.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.dll": {}}}, "Serilog.AspNetCore/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Serilog": "4.0.0", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Settings.Configuration": "8.0.2", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Serilog.AspNetCore.dll": {}}}, "Serilog.Enrichers.GlobalLogContext/3.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net6.0/Serilog.Enrichers.GlobalLogContext.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Serilog.Enrichers.GlobalLogContext.dll": {}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Serilog": "4.0.0", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Extensions.Logging.dll": {}}}, "Serilog.Formatting.Compact/2.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net7.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Serilog.Formatting.Compact.dll": {}}}, "Serilog.Settings.Configuration/8.0.2": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.1", "Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Settings.Configuration.dll": {}}}, "Serilog.Sinks.ApplicationInsights/4.0.0": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Serilog": "4.0.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.ApplicationInsights.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Serilog.Sinks.ApplicationInsights.dll": {}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Serilog.Sinks.Console.dll": {}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Serilog.Sinks.File.dll": {}}}, "Serilog.Sinks.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {}}}, "Serilog.Sinks.Logz.Io/7.1.0": {"dependencies": {"Elastic.CommonSchema.Serilog": "1.5.3", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Http": "8.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "4.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.0.0"}}, "compile": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {}}}, "Serilog.Sinks.Seq/6.0.0": {"dependencies": {"Serilog": "4.0.0", "Serilog.Formatting.Compact": "2.0.0", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Seq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Serilog.Sinks.Seq.dll": {}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}, "compile": {"lib/net5.0/SharpCompress.dll": {}}}, "Shouldly/4.2.1": {"dependencies": {"DiffEngine": "11.3.0", "EmptyFiles": "4.4.0"}, "runtime": {"lib/net5.0/Shouldly.dll": {"assemblyVersion": "4.2.1.0", "fileVersion": "4.2.1.0"}}, "compile": {"lib/net5.0/Shouldly.dll": {}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net5.0/Snappier.dll": {}}}, "starkbank-ecdsa/1.3.3": {"runtime": {"lib/netstandard2.1/StarkbankEcdsa.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/StarkbankEcdsa.dll": {}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "9.0.1", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Annotations/9.0.1": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "9.0.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.Annotations.dll": {}}}, "Swashbuckle.AspNetCore.Swagger/9.0.1": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.1"}, "runtime": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.1.1541"}}, "compile": {"lib/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "6.5.0.0", "fileVersion": "6.5.0.0"}}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.0.0": {"dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24.5302"}}, "compile": {"lib/net6.0/System.ClientModel.dll": {}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.CodeDom.dll": {}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41905"}}, "compile": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.1"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.Drawing.Common.dll": {}}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Packaging/6.0.0": {"runtime": {"lib/net6.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.IO.Packaging.dll": {}}}, "System.IO.Pipelines/8.0.0": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Management/6.0.1": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.1623.17311"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.1623.17311"}}, "compile": {"lib/net6.0/System.Management.dll": {}}}, "System.Memory/4.5.5": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.Security.Permissions.dll": {}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.4": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.Windows.Extensions.dll": {}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "TinyHelpers/3.0.2": {"dependencies": {"System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/TinyHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.5254"}}, "compile": {"lib/net8.0/TinyHelpers.dll": {}}}, "TinyHelpers.EntityFrameworkCore/3.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "TinyHelpers": "3.0.2"}, "runtime": {"lib/net8.0/TinyHelpers.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.17739"}}, "compile": {"lib/net8.0/TinyHelpers.EntityFrameworkCore.dll": {}}}, "XLParser/1.5.2": {"dependencies": {"Irony.NetCore": "1.0.11", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.6/XLParser.dll": {"assemblyVersion": "1.5.2.0", "fileVersion": "1.5.2.0"}}, "compile": {"lib/netstandard1.6/XLParser.dll": {}}}, "xunit/2.9.2": {"dependencies": {"xunit.analyzers": "1.16.0", "xunit.assert": "2.9.2", "xunit.core": "2.9.2"}}, "xunit.abstractions/2.0.3": {"runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/xunit.abstractions.dll": {}}}, "xunit.analyzers/1.16.0": {}, "xunit.assert/2.9.2": {"runtime": {"lib/net6.0/xunit.assert.dll": {"assemblyVersion": "2.9.2.0", "fileVersion": "2.9.2.0"}}, "compile": {"lib/net6.0/xunit.assert.dll": {}}}, "xunit.core/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2", "xunit.extensibility.execution": "2.9.2"}}, "xunit.extensibility.core/2.9.2": {"dependencies": {"xunit.abstractions": "2.0.3"}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"assemblyVersion": "2.9.2.0", "fileVersion": "2.9.2.0"}}, "compile": {"lib/netstandard1.1/xunit.core.dll": {}}}, "xunit.extensibility.execution/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2"}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"assemblyVersion": "2.9.2.0", "fileVersion": "2.9.2.0"}}, "compile": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {}}}, "xunit.runner.visualstudio/3.0.0": {}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "0.7.3.0", "fileVersion": "0.7.3.0"}}, "compile": {"lib/net7.0/ZstdSharp.dll": {}}}, "BlueTape.DataAccess.Mongo/1.0.0": {"dependencies": {"BlueTape.MongoDB": "1.1.32", "BlueTape.Services.LMS.Infrastructure": "1.0.0"}, "runtime": {"BlueTape.DataAccess.Mongo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.DataAccess.Mongo.dll": {}}}, "BlueTape.OBS.Client/1.0.0": {"dependencies": {"BlueTape.OBS": "1.6.72", "BlueTape.Utilities": "1.4.6", "DateOnlyTimeOnly.AspNet": "2.1.1", "Macross.Json.Extensions": "3.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"BlueTape.OBS.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.OBS.Client.dll": {}}}, "BlueTape.Services.ARS/1.0.0": {"dependencies": {"BlueTape.Services.ARS.DataAccess": "1.0.0", "BlueTape.Services.ARS.Models": "1.0.0", "BlueTape.Services.LMS.Application": "1.0.0", "BlueTape.Services.LMS.Domain": "1.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "BlueTape.Utilities": "1.4.6"}, "runtime": {"BlueTape.Services.ARS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.ARS.dll": {}}}, "BlueTape.Services.ARS.DataAccess/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.Services.LMS.DataAccess": "1.0.0", "BlueTape.Services.LMS.Domain": "1.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.0"}, "runtime": {"BlueTape.Services.ARS.DataAccess.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.ARS.DataAccess.dll": {}}}, "BlueTape.Services.ARS.Models/1.0.0": {"runtime": {"BlueTape.Services.ARS.Models.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.ARS.Models.dll": {}}}, "BlueTape.Services.DataAccess.External/1.0.0": {"dependencies": {"BlueTape.CompanyClient": "1.0.53", "BlueTape.CompanyService": "1.3.4", "BlueTape.CompanyService.Common": "1.1.21", "BlueTape.InvoiceClient": "1.0.21", "BlueTape.InvoiceService": "1.0.40", "BlueTape.InvoiceService.Common": "1.1.3", "BlueTape.LinqpalClient": "1.0.8", "BlueTape.OBS": "1.6.72", "BlueTape.OBS.Client": "1.0.0", "BlueTape.PaymentService": "1.0.15", "BlueTape.ServiceBusMessaging": "1.0.9"}, "runtime": {"BlueTape.Services.DataAccess.External.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.DataAccess.External.dll": {}}}, "BlueTape.Services.LMS.API/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.EnumMapping": "3.1.0", "AutoMapper.Extensions.ExpressionMapping": "6.0.4", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.LS": "1.1.78", "BlueTape.LS.Domain": "1.1.36", "BlueTape.Services.ARS": "1.0.0", "BlueTape.Services.ARS.Models": "1.0.0", "BlueTape.Services.LMS.Application": "1.0.0", "BlueTape.Services.LMS.AutoPay": "1.0.0", "BlueTape.Services.LMS.Infrastructure.Hosting": "1.0.0", "BlueTape.Services.LMS.MonitoringService": "1.0.0", "BlueTape.Services.Reporting": "1.0.0", "BlueTape.Utilities": "1.4.6", "DateOnlyTimeOnly.AspNet": "2.1.1", "DateOnlyTimeOnly.AspNet.Swashbuckle": "2.1.1", "FluentValidation": "11.9.0", "FluentValidation.AspNetCore": "11.3.0", "FluentValidation.DependencyInjectionExtensions": "11.9.0", "Microsoft.ApplicationInsights.AspNetCore": "2.22.0", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.0", "Quartz": "3.8.0", "Quartz.Extensions.DependencyInjection": "3.8.0", "Quartz.Extensions.Hosting": "3.8.0", "Serilog.Sinks.ApplicationInsights": "4.0.0", "Serilog.Sinks.Seq": "6.0.0", "Swashbuckle.AspNetCore": "6.5.0", "Swashbuckle.AspNetCore.Annotations": "9.0.1", "BlueTape.AzureKeyVault": "1.0.3"}, "runtime": {"BlueTape.Services.LMS.API.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.API.dll": {}}}, "BlueTape.Services.LMS.Application/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.CompanyService": "1.3.4", "BlueTape.CompanyService.Common": "1.1.21", "BlueTape.DataAccess.Mongo": "1.0.0", "BlueTape.DraftsMapper": "1.0.1", "BlueTape.InvoiceService": "1.0.40", "BlueTape.InvoiceService.Common": "1.1.3", "BlueTape.LMS": "1.0.2", "BlueTape.Notification.Sender": "1.0.5", "BlueTape.OBS": "1.6.72", "BlueTape.OBS.Client": "1.0.0", "BlueTape.Services.ARS.Models": "1.0.0", "BlueTape.Services.DataAccess.External": "1.0.0", "BlueTape.Services.LMS.DataAccess": "1.0.0", "BlueTape.Services.LMS.DataAccess.Company": "1.0.0", "BlueTape.Services.LMS.DataAccess.SOFR": "1.0.0", "BlueTape.Services.LMS.Domain": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0", "BlueTape.Services.LMS.UnitOfWork": "1.0.0", "BlueTape.Services.Reporting": "1.0.0", "BlueTape.Utilities": "1.4.6", "FluentValidation": "11.9.0", "FluentValidation.DependencyInjectionExtensions": "11.9.0", "BlueTape.LS": "1.1.78", "BlueTape.LS.Domain": "1.1.36"}, "runtime": {"BlueTape.Services.LMS.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.Application.dll": {}}}, "BlueTape.Services.LMS.AutoPay/1.0.0": {"dependencies": {"BlueTape.Services.LMS.Application": "1.0.0"}, "runtime": {"BlueTape.Services.LMS.AutoPay.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.AutoPay.dll": {}}}, "BlueTape.Services.LMS.DataAccess/1.0.0": {"dependencies": {"AutoFilter.Sql": "2.0.0", "BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Services.LMS.Domain": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0", "BlueTape.Services.Reporting.Domain": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.InMemory": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.0", "TinyHelpers.EntityFrameworkCore": "3.0.2"}, "runtime": {"BlueTape.Services.LMS.DataAccess.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.DataAccess.dll": {}}}, "BlueTape.Services.LMS.DataAccess.Company/1.0.0": {"dependencies": {"BlueTape.CompanyClient": "1.0.53", "BlueTape.CompanyService": "1.3.4", "BlueTape.CompanyService.Common": "1.1.21", "BlueTape.DataAccess.Mongo": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0"}, "runtime": {"BlueTape.Services.LMS.DataAccess.Company.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.DataAccess.Company.dll": {}}}, "BlueTape.Services.LMS.DataAccess.SOFR/1.0.0": {"dependencies": {"BlueTape.Services.LMS.Domain": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0", "BlueTape.AzureKeyVault": "1.0.3"}, "runtime": {"BlueTape.Services.LMS.DataAccess.SOFR.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.DataAccess.SOFR.dll": {}}}, "BlueTape.Services.LMS.Domain/1.0.0": {"dependencies": {"Audit.EntityFramework.Core": "27.1.1", "AutoFilter.Sql": "2.0.0", "BlueTape.LS.Domain": "1.1.36", "BlueTape.OBS": "1.6.72", "BlueTape.Utilities": "1.4.6", "BlueTape.LS": "1.1.78"}, "runtime": {"BlueTape.Services.LMS.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.Domain.dll": {}}}, "BlueTape.Services.LMS.Infrastructure/1.0.0": {"runtime": {"BlueTape.Services.LMS.Infrastructure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.Infrastructure.dll": {}}}, "BlueTape.Services.LMS.Infrastructure.Hosting/1.0.0": {"dependencies": {"Azure.Extensions.AspNetCore.Configuration.Secrets": "1.3.2", "Azure.Identity": "1.10.4", "BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Services.LMS.Application": "1.0.0", "BlueTape.Services.LMS.AutoPay": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0", "Microsoft.ApplicationInsights.WorkerService": "2.21.0", "Microsoft.Azure.Functions.Worker.ApplicationInsights": "1.1.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.1", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Serilog.Enrichers.GlobalLogContext": "3.0.0", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Settings.Configuration": "8.0.2", "Serilog.Sinks.ApplicationInsights": "4.0.0", "Serilog.Sinks.Console": "6.0.0"}, "runtime": {"BlueTape.Services.LMS.Infrastructure.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.Infrastructure.Hosting.dll": {}}}, "BlueTape.Services.LMS.MonitoringService/1.0.0": {"dependencies": {"BlueTape.AWSS3": "1.1.6", "BlueTape.DataAccess.Mongo": "1.0.0", "BlueTape.Notification.Sender": "1.0.5", "BlueTape.Services.LMS.DataAccess": "1.0.0", "BlueTape.Services.Reporting": "1.0.0", "Quartz": "3.8.0", "Quartz.Extensions.DependencyInjection": "3.8.0", "Quartz.Extensions.Hosting": "3.8.0"}, "runtime": {"BlueTape.Services.LMS.MonitoringService.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.MonitoringService.dll": {}}}, "BlueTape.Services.LMS.UnitOfWork/1.0.0": {"dependencies": {"BlueTape.Services.LMS.DataAccess": "1.0.0"}, "runtime": {"BlueTape.Services.LMS.UnitOfWork.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.LMS.UnitOfWork.dll": {}}}, "BlueTape.Services.Reporting/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.Common.FileService": "1.0.6", "BlueTape.CompanyClient": "1.0.53", "BlueTape.CompanyService": "1.3.4", "BlueTape.DataAccess.Mongo": "1.0.0", "BlueTape.OBS.Client": "1.0.0", "BlueTape.Services.DataAccess.External": "1.0.0", "BlueTape.Services.LMS.DataAccess.Company": "1.0.0", "BlueTape.Services.Reporting.DataAccess": "1.0.0", "BlueTape.Services.Reporting.Domain": "1.0.0", "BlueTape.AWSS3": "1.1.6", "BlueTape.AzureKeyVault": "1.0.3", "BlueTape.EmailSender": "3.0.7"}, "runtime": {"BlueTape.Services.Reporting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.Reporting.dll": {}}}, "BlueTape.Services.Reporting.DataAccess/1.0.0": {"dependencies": {"BlueTape.Services.LMS.DataAccess": "1.0.0", "BlueTape.Services.Reporting.Domain": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"BlueTape.Services.Reporting.DataAccess.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.Reporting.DataAccess.dll": {}}}, "BlueTape.Services.Reporting.Domain/1.0.0": {"dependencies": {"BlueTape.Services.LMS.Domain": "1.0.0"}, "runtime": {"BlueTape.Services.Reporting.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"BlueTape.Services.Reporting.Domain.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/*******": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/*******": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/*******": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/*******": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/*******": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Reference/*******": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features/*******": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http.Reference/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/*******": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/*******": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/1*******": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives.Reference/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry.Reference/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext.Reference/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent.Reference/*******": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Reference/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable.Reference/*******": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/*******": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console.Reference/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug.Reference/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog.Reference/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools.Reference/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing.Reference/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/*******": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Formats.Tar/*******": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars.Reference/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Reference/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions.Reference/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression.Reference/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile.Reference/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.Reference/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl.Reference/*******": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Reference/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives.Reference/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines.Reference/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/*******": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.Reference/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions.Reference/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http.Reference/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/*******": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives.Reference/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/*******": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets.Reference/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors.Reference/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel.Reference/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Reference/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Reference/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight.Reference/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions.Reference/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/*******": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives.Reference/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions.Reference/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager.Reference/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime.Reference/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions.Reference/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles.Reference/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.Reference/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/*******": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics.Reference/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/*******": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl.Reference/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms.Reference/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp.Reference/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/*******": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding.Reference/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl.Reference/*******": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives.Reference/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Reference/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions.Reference/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web.Reference/*******": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json.Reference/*******": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions.Reference/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading.Reference/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting/*******": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Reference/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer.Reference/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter.Reference/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument.Reference/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"BlueTape.Services.ARS.IntegrationTests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Audit.EntityFramework.Core/27.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h2qzCHOoKesGiaKYFIkXKDdXluNXeBJmu5QyphEzJyxYSHqX3L4GPgqhs6Aa/f0JFuTIUVup+h8IoHryjoMMag==", "path": "audit.entityframework.core/27.1.1", "hashPath": "audit.entityframework.core.27.1.1.nupkg.sha512"}, "Audit.NET/27.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-M8CVk8EGtgDJoCU+TPivuGffrpDYa9M78Wahqm9cXMVsdUItBVAte9aVi9NWVF0DmDZx6iw4ikp2IXfEWQ0GIw==", "path": "audit.net/27.1.1", "hashPath": "audit.net.27.1.1.nupkg.sha512"}, "AutoFilter.Sql/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7J2H9GPLQvU21rxg2hpVfgq5KfuPSFk7yLcLZQsxtQJvst7W5epDxhkQ7UGnfTd+cipGKpDj3hUTuzkg+0xA8A==", "path": "autofilter.sql/2.0.0", "hashPath": "autofilter.sql.2.0.0.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.EnumMapping/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-8cH94J1ExLkMf1L9GOij2t/8KeNnjRd8Rz4q0geFTZxrilLe61/0r4WHb2KGQKOF/wGsFJNeB97sD6qglpGhVQ==", "path": "automapper.extensions.enummapping/3.1.0", "hashPath": "automapper.extensions.enummapping.3.1.0.nupkg.sha512"}, "AutoMapper.Extensions.ExpressionMapping/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-scgLSSt/Ju2Fhg5xn8Ckdebg+mh8MQ9bQpTBvxj9jQP0kinGmtcsg9BxppnKffYVaec16GvbrKmWI6z1Ce6TPg==", "path": "automapper.extensions.expressionmapping/6.0.4", "hashPath": "automapper.extensions.expressionmapping.6.0.4.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "AWSSDK.Core/3.7.302.6": {"type": "package", "serviceable": true, "sha512": "sha512-eOPMHT3YSLQg5wXm+UL8Qoka17byK5l/4qxfuE/S32IoQ34gkTDQ1yI9pQPk8LEpeV/W0BE2np+XuhdDQ3wE8g==", "path": "awssdk.core/3.7.302.6", "hashPath": "awssdk.core.3.7.302.6.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/3.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iFjbEnVB0f6Hr8L3EfdelHG7zxVQrOmeP9UIrX3IODR1eTsrqVrmq0mazdIr0GZK1YG2/DZiVt6tNyV1bayndw==", "path": "awssdk.extensions.netcore.setup/3.7.2", "hashPath": "awssdk.extensions.netcore.setup.3.7.2.nupkg.sha512"}, "AWSSDK.KeyManagementService/3.7.300.46": {"type": "package", "serviceable": true, "sha512": "sha512-6J3FlWpFvEhYinkmabY8i9w66lgvmjlZG5oRHazqyiyatjMOYsA76Ynj+T6gtHC5iB1BrBtEFOPO1rq36sILfA==", "path": "awssdk.keymanagementservice/3.7.300.46", "hashPath": "awssdk.keymanagementservice.3.7.300.46.nupkg.sha512"}, "AWSSDK.S3/3.7.10": {"type": "package", "serviceable": true, "sha512": "sha512-vsNA29rhuwfILkHhPvpm2DHQaqn2TndpwStmhQMnqTRGB+mL1IG+xAdicEIrWrdOsAP4SD9JONYe9CJMcXcnUg==", "path": "awssdk.s3/3.7.10", "hashPath": "awssdk.s3.3.7.10.nupkg.sha512"}, "AWSSDK.SecretsManager/3.7.302.21": {"type": "package", "serviceable": true, "sha512": "sha512-Ns8mQtXUWAhp32xJTeftmAklzQeu0Tful0BTZjQg5EeyYE+qUDQgCrY31UBUr1PVw2sInmT5BBzoyhic9OjAwA==", "path": "awssdk.secretsmanager/3.7.302.21", "hashPath": "awssdk.secretsmanager.3.7.302.21.nupkg.sha512"}, "AWSSDK.SecretsManager.Caching/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N982+rohMJ/w8ywyN6hgnSgw2cpqj4MJDizz+b93gudQSEzR3lCTzHGN3AQl+ngMH4yTG+DfKgmL7QajvvYyKQ==", "path": "awssdk.secretsmanager.caching/1.0.6", "hashPath": "awssdk.secretsmanager.caching.1.0.6.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.300.47": {"type": "package", "serviceable": true, "sha512": "sha512-wBcBC0axwf+qQTVMPUblDxIpMtbp04z9ElYeW6BSjeAO7spvJXhEz7yhikdTn8YaeWW+K6U9h6AIDQpJlhH2vQ==", "path": "awssdk.securitytoken/3.7.300.47", "hashPath": "awssdk.securitytoken.3.7.300.47.nupkg.sha512"}, "AWSSDK.SimpleNotificationService/**********": {"type": "package", "serviceable": true, "sha512": "sha512-RqBqzwh9dGCxmfaUGvdHyYqBzd4RyCGUdd8w8zo4nxCH/3acKDbQriiFRgYvSOkOunIKXNMR45aBbQXeReuVMw==", "path": "awssdk.simplenotificationservice/**********", "hashPath": "awssdk.simplenotificationservice.**********.nupkg.sha512"}, "Azure.Core/1.42.0": {"type": "package", "serviceable": true, "sha512": "sha512-Fg88OsrjD2nAvz3N0pk2d/AwIHQRrs9CjA9A35OW1YgYhMo0OTz4WkntQK6V2tf84g7SnfJM8ORcZl+bH6P9Cg==", "path": "azure.core/1.42.0", "hashPath": "azure.core.1.42.0.nupkg.sha512"}, "Azure.Core.Amqp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6GG4gyFkAuHtpBVkvj0wE5+lCM+ttsZlIWAipBkI+jlCUlTgrTiNUROBFnb8xuKoymVDw9Tf1W8RoKqgbd71lg==", "path": "azure.core.amqp/1.3.0", "hashPath": "azure.core.amqp.1.3.0.nupkg.sha512"}, "Azure.Extensions.AspNetCore.Configuration.Secrets/1.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uMIU++B4XV4lW8+59rKi2Qph6tj2V7tHyLcwVR7OQRmA8cX0VVFhj2DyMahhoF9j4Jk99WA08Nsznd5RwC5Zfw==", "path": "azure.extensions.aspnetcore.configuration.secrets/1.3.2", "hashPath": "azure.extensions.aspnetcore.configuration.secrets.1.3.2.nupkg.sha512"}, "Azure.Identity/1.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-hSvisZy9sld0Gik1X94od3+rRXCx+AKgi+iLH6fFdlnRZRePn7RtrqUGSsORiH2h8H2sc4NLTrnuUte1WL+QuQ==", "path": "azure.identity/1.10.4", "hashPath": "azure.identity.1.10.4.nupkg.sha512"}, "Azure.Messaging.ServiceBus/7.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-RvpLKmp2ur7hfm7NqiKPY2wIU7O4+yajYm3w7etnDsNj6sMlLCyNyCNMgVGeudQ4nOrk0YtoHJ1SbX2nJpCsUw==", "path": "azure.messaging.servicebus/7.17.1", "hashPath": "azure.messaging.servicebus.7.17.1.nupkg.sha512"}, "Azure.Security.KeyVault.Keys/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-HnW9kjhRzQkfJE4ISl63cWVa6qLe3FM1MxoxNvNFtDUeT5iMBEg0YgGbcx2YgEiYaazIvSgZyjBr4L3Ur3+m7g==", "path": "azure.security.keyvault.keys/4.5.0", "hashPath": "azure.security.keyvault.keys.4.5.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwPceoznuT6glvirZcXlaCQrh1uzTSxpZUi2hRFNumHiS3hVyqIXI5fgWiLtlBzwqPJMTr0flUoSvGKjXXQlfg==", "path": "azure.security.keyvault.secrets/4.6.0", "hashPath": "azure.security.keyvault.secrets.4.6.0.nupkg.sha512"}, "BlueTape.AWSS3/1.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-kzpVau1mZ7zVn7X6dsxDQ9KIK256xK4O8yeTgDgPFjNJGpuZpW8ntAllk2lXSxhW9tXtoG7fiRO32TkWIeNXaw==", "path": "bluetape.awss3/1.1.6", "hashPath": "bluetape.awss3.1.1.6.nupkg.sha512"}, "BlueTape.AzureKeyVault/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-On4RZI41X71GSmrYnv3p1eN6ullTWF9L8SsZ2NC/tQsQ/Upe0X1kcJE7rgvrT6G1RToKyY2n+OAGwfYZH8uOlQ==", "path": "bluetape.azurekeyvault/1.0.3", "hashPath": "bluetape.azurekeyvault.1.0.3.nupkg.sha512"}, "BlueTape.Common.ExceptionHandling/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-/xF77tfLqtZck0m82ynWFshDGYCWEAlqS3iUiSYrM37GF3J9l/kw/AA6T2LJlmePnyGGVwufPIJQpFcd/2dhbQ==", "path": "bluetape.common.exceptionhandling/1.0.8", "hashPath": "bluetape.common.exceptionhandling.1.0.8.nupkg.sha512"}, "BlueTape.Common.Extensions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-P4mQRSipiN2qp+5ETfnBdZz564U1AWder867Zj/4SV+mZTS+SxqML0H+sW2W7PADp9iUgdngoNzqYsj+UxGheA==", "path": "bluetape.common.extensions/1.1.0", "hashPath": "bluetape.common.extensions.1.1.0.nupkg.sha512"}, "BlueTape.Common.FileService/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-8N5R1YPx1oe6w9iU1je5NKg085RVumKDtDzvjLRqYqCbZSr+C47dKe2XjUrLlttrqebqagFdKFs0eLMu94n3HQ==", "path": "bluetape.common.fileservice/1.0.6", "hashPath": "bluetape.common.fileservice.1.0.6.nupkg.sha512"}, "BlueTape.Common.Validation/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-IA6/J881MMavf8qQ28vuZTvcaCU0gOAhZNXZxvtvs9BgXUHlWUx0LWfD9pwKTPNTiqqUub4XFeC34pXgO4bA0w==", "path": "bluetape.common.validation/1.0.4", "hashPath": "bluetape.common.validation.1.0.4.nupkg.sha512"}, "BlueTape.CompanyClient/1.0.53": {"type": "package", "serviceable": true, "sha512": "sha512-Jsl9IhdSskCZU986n7LxmwdFHMPMPQwqWoGOCK5Ich8X5xcEHOFdr3ish8MBZslciFzXuVvDueHJVFKvFWeZzQ==", "path": "bluetape.companyclient/1.0.53", "hashPath": "bluetape.companyclient.1.0.53.nupkg.sha512"}, "BlueTape.CompanyService/1.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-45U106omsh6weMvGaBSf3fY+yswpW/tsLXvHBJKZgjh40tYYe6j7gMrCXC8mqvCZ+wnNp1NryP3oPsR9P8PP1w==", "path": "bluetape.companyservice/1.3.4", "hashPath": "bluetape.companyservice.1.3.4.nupkg.sha512"}, "BlueTape.CompanyService.Common/1.1.21": {"type": "package", "serviceable": true, "sha512": "sha512-1znqFDa5Gyglpcp3Eq3vh6r88ahgWhhMg6Duja1guXhjdWiRDDbIN8wPYoN5+i/IeW/aqLlyTr7pxapJ28qxwg==", "path": "bluetape.companyservice.common/1.1.21", "hashPath": "bluetape.companyservice.common.1.1.21.nupkg.sha512"}, "BlueTape.DraftsMapper/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wZORj2Yb3Z4OO8B838+y33Mc7c1V5wssBRo1sHP3B1sCo019SmN+0/qdbVBfNLujv5DZO8NdrdCngJIlbE5BEg==", "path": "bluetape.draftsmapper/1.0.1", "hashPath": "bluetape.draftsmapper.1.0.1.nupkg.sha512"}, "BlueTape.EmailSender/3.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-/qaKlPPGOu/EvdKdGGKvq9xKpRq/37hrqzX/TgtQhUkWmEydufTdilB93R7lo7owIpkNkadbsbgPjUHlA1HzXA==", "path": "bluetape.emailsender/3.0.7", "hashPath": "bluetape.emailsender.3.0.7.nupkg.sha512"}, "BlueTape.InvoiceClient/1.0.21": {"type": "package", "serviceable": true, "sha512": "sha512-Pp/hOsOzFTmHpzjQgYndv8AyxX8cmT8/XVN5cHkHEsC3k8y/Vs8+ATspCCDoz3oqBRBnZQCVmmkWo2sVrKMbVQ==", "path": "bluetape.invoiceclient/1.0.21", "hashPath": "bluetape.invoiceclient.1.0.21.nupkg.sha512"}, "BlueTape.InvoiceService/1.0.40": {"type": "package", "serviceable": true, "sha512": "sha512-ot/eDvPfaZ0J935VDR0r6okalj1/fRc+L5+rb5zXE9ueyQVmGHHrDtEc288MUQ3pm0IC8XMxHOzDASMM3zGREw==", "path": "bluetape.invoiceservice/1.0.40", "hashPath": "bluetape.invoiceservice.1.0.40.nupkg.sha512"}, "BlueTape.InvoiceService.Common/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-bd3nhrpnFHZ4ntWpVcs46EmK1bf/+DSSKy23aoo6ROYfRLD4R9Ky9WSsQKC7kIv3I3gxVd7uOFRQfb71WrVyqQ==", "path": "bluetape.invoiceservice.common/1.1.3", "hashPath": "bluetape.invoiceservice.common.1.1.3.nupkg.sha512"}, "BlueTape.LinqpalClient/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-5NZ70zF50ugY62Ar1WWJZn6fTwL3RaoI2fa5bh6w/bzbznb8ve+NpRnQi0KFzOBmzVp3TbFfVG6CngKdg1e+PA==", "path": "bluetape.linqpalclient/1.0.8", "hashPath": "bluetape.linqpalclient.1.0.8.nupkg.sha512"}, "BlueTape.LMS/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dfGp9ZySFgXhDC8/t2hb1HY4Kb5UioxY0Bhjlk3zSY0RXP5UUKLyEs0Ii2SrL8gWlmuwKlLYW8hPbJ1hjasZpA==", "path": "bluetape.lms/1.0.2", "hashPath": "bluetape.lms.1.0.2.nupkg.sha512"}, "BlueTape.LS/1.1.78": {"type": "package", "serviceable": true, "sha512": "sha512-u8xaG9I3XDkoVkvkzIZJGx1DePnBKowm+FJ9DjOtSqzbYe5FNAJJpW8CIiqjYQxmqhyql+vBIdWQ5mkUijDZnQ==", "path": "bluetape.ls/1.1.78", "hashPath": "bluetape.ls.1.1.78.nupkg.sha512"}, "BlueTape.LS.Domain/1.1.36": {"type": "package", "serviceable": true, "sha512": "sha512-GVGfugV3OtXFHnjm1z429MCf6+Exu/wEXsWtUgqUeSnx2DHh+/MYsEk7Ruaw/daCkdL/cy2rIUerCUkLdgjDuA==", "path": "bluetape.ls.domain/1.1.36", "hashPath": "bluetape.ls.domain.1.1.36.nupkg.sha512"}, "BlueTape.MongoDB/1.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-oeiZKvlFUy87kEQbyOthESrOn4tbsBUBDXIZ6NqpxcSqGIODpKyn6symjuNNoQMIdPbxAOu1yf68yRXmF5M8OQ==", "path": "bluetape.mongodb/1.1.32", "hashPath": "bluetape.mongodb.1.1.32.nupkg.sha512"}, "BlueTape.Notification.Sender/1.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-wvvBkebdqo1BDvco81tiKFf21MYZRdNJVP6f6cfNlF3I4tQ1+aMzUdy/Tmkg25EMawvvXsj62zDlBw7IoOV7WA==", "path": "bluetape.notification.sender/1.0.5", "hashPath": "bluetape.notification.sender.1.0.5.nupkg.sha512"}, "BlueTape.OBS/1.6.72": {"type": "package", "serviceable": true, "sha512": "sha512-cSgIO2r8PB605JHbx4HLnkQ5YPUqQKlIpPBd1s7dk5v8HMB+GQZKloWycCWzVyV5m6yZJy5A91ecvKqAflK1KQ==", "path": "bluetape.obs/1.6.72", "hashPath": "bluetape.obs.1.6.72.nupkg.sha512"}, "BlueTape.PaymentService/1.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-eomwRAiEvjNOtNzQDlPKP3ILus6f8JpP8Cmg4Xaqf068VQvo22OwXyiEVaJ8rrhWW8cVtoO9w90YIY0qSnIWqg==", "path": "bluetape.paymentservice/1.0.15", "hashPath": "bluetape.paymentservice.1.0.15.nupkg.sha512"}, "BlueTape.ServiceBusMessaging/1.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-+zUFfrqv4Qu4uSGVOZ64tOBygVA/UfDtN2amwgfFF6vyNxnkRhJXeJyepeMOekrdA0wzUbB7U/OrNO7wrvX94g==", "path": "bluetape.servicebusmessaging/1.0.9", "hashPath": "bluetape.servicebusmessaging.1.0.9.nupkg.sha512"}, "BlueTape.SNS/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-yyQoT3RXeiKRFiv/RK6gPVXj4rAR7OXrAUDlIU/f8EztzUgiLsNzazfmzCJRSgLxfmKSJoBkdfqcyBla3Rxkwg==", "path": "bluetape.sns/1.0.2", "hashPath": "bluetape.sns.1.0.2.nupkg.sha512"}, "BlueTape.Utilities/1.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-vD/c4SBIubmwdz8okn2XBUfUMc5kjMOaQJDckOgFSFyhhPjz0JD9sGkHNXq7Py0x8DXujuWSLtVVh77FXS3DMA==", "path": "bluetape.utilities/1.4.6", "hashPath": "bluetape.utilities.1.4.6.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "ClosedXML/0.102.3": {"type": "package", "serviceable": true, "sha512": "sha512-4PN5JZsloPGfV2iSa2g62CSETQAtHELECwo9rzMvOAibe1fsPv0CNTOXXji+ihXOyEZBZDLhkE7s1MhHJzMxIA==", "path": "closedxml/0.102.3", "hashPath": "closedxml.0.102.3.nupkg.sha512"}, "coverlet.collector/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-SvLbRq7gjzE34BI90vP6ge812+PAjinNoKhdFZHwVEu/ozJgZY+0KyDh1K0teDeMeuzQJuF8OvleRBYXsZDz0A==", "path": "coverlet.collector/6.0.3", "hashPath": "coverlet.collector.6.0.3.nupkg.sha512"}, "CsvHelper/32.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-JNDkd4Wc2Tim5IuyL8Ai13RhzRvaHItseEvlJ2YRkx2SMBFrIsW8a5+/o4/O1jsb6mlviihR6Hfyn+nqY6CurA==", "path": "csvhelper/32.0.3", "hashPath": "csvhelper.32.0.3.nupkg.sha512"}, "DateOnlyTimeOnly.AspNet/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-7HlGV6sm0efRMquxnZaoqV3KybUMOs1IaJ4TaX85v7IZF01h1YV6Zscos9g4qQfrGYfvqLjbpe3Q54cmMBeISQ==", "path": "dateonlytimeonly.aspnet/2.1.1", "hashPath": "dateonlytimeonly.aspnet.2.1.1.nupkg.sha512"}, "DateOnlyTimeOnly.AspNet.Swashbuckle/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-X2k2yelbE2jyBI/pOv5T615ell5Rhmb31MV1mM0Q+VpqNUran20gdGz9MBP9dbAcbH1EQ/l/s/EcX/+Bp365jQ==", "path": "dateonlytimeonly.aspnet.swashbuckle/2.1.1", "hashPath": "dateonlytimeonly.aspnet.swashbuckle.2.1.1.nupkg.sha512"}, "DiffEngine/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k0ZgZqd09jLZQjR8FyQbSQE86Q7QZnjEzq1LPHtj1R2AoWO8sjV5x+jlSisL7NZAbUOI4y+7Bog8gkr9WIRBGw==", "path": "diffengine/11.3.0", "hashPath": "diffengine.11.3.0.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DocumentFormat.OpenXml/2.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-RhpnDgyyx1KjZm98T3w3bSXFHG/7ZNUaVmz4NAUA+jmV3PcVNZeW87Y04CpBNLdDHEMSspirfo0B5kLRaoE97w==", "path": "documentformat.openxml/2.16.0", "hashPath": "documentformat.openxml.2.16.0.nupkg.sha512"}, "Elastic.CommonSchema/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-JgwhfThYY/s17asUiBCUVqnZtDdGTWO/2hTPG01QDfw2+T6kfwskrj5eh6XpBZsOh6r9SpBL95vSsU+q44i7Zg==", "path": "elastic.commonschema/1.5.3", "hashPath": "elastic.commonschema.1.5.3.nupkg.sha512"}, "Elastic.CommonSchema.Serilog/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-bp2qHOWmN15fTKUecFMt7oCra68I1cm3yAEmwPcLuz4v2pQ5YxC8nVtyCTSSibquUS/ZPH5JInjlmuywV3UoyQ==", "path": "elastic.commonschema.serilog/1.5.3", "hashPath": "elastic.commonschema.serilog.1.5.3.nupkg.sha512"}, "EmptyFiles/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-gwJEfIGS7FhykvtZoscwXj/XwW+mJY6UbAZk+qtLKFUGWC95kfKXnj8VkxsZQnWBxJemM/q664rGLN5nf+OHZw==", "path": "emptyfiles/4.4.0", "hashPath": "emptyfiles.4.4.0.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "FluentValidation/11.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-VneVlTvwYDkfHV5av3QrQ0amALgrLX6LV94wlYyEsh0B/klJBW7C8y2eAtj5tOZ3jH6CAVpr4s1ZGgew/QWyig==", "path": "fluentvalidation/11.9.0", "hashPath": "fluentvalidation.11.9.0.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>++xvN7HUf4WlHJL6bhsybUj/uho8ApOYIdxGjpF8Ot7Fukz6LRfRJ06H0KXhWqmMHWEbu89hJbjKJHtg7b9g==", "path": "fluentvalidation.dependencyinjectionextensions/11.9.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.9.0.nupkg.sha512"}, "Irony.NetCore/1.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-/2MCkqdhEjPiSTZKYXg6u9AO9o3m68biqcFHAnM28WdnrcZii2vYbmKPzT5ejByyV72FUlygOtHBMt8SLnUyNw==", "path": "irony.netcore/1.0.11", "hashPath": "irony.netcore.1.0.11.nupkg.sha512"}, "libphonenumber-csharp/8.12.45": {"type": "package", "serviceable": true, "sha512": "sha512-TG+5eh+rJ15LTFFLA/J179O/NwVTpdjreJxWYWSCbT8kFVAhSra9Ap5twi4LFJTMe9zkYxxBHNlnDv8gPfbaHA==", "path": "libphonenumber-csharp/8.12.45", "hashPath": "libphonenumber-csharp.8.12.45.nupkg.sha512"}, "Macross.Json.Extensions/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AkNshs6dopj8FXsmkkJxvLivN2SyDJQDbjcds5lo9+Y6L4zpcoXdmzXQ3VVN+AIWQr0CTD5A7vkuHGAr2aypZg==", "path": "macross.json.extensions/3.0.0", "hashPath": "macross.json.extensions.3.0.0.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "path": "microsoft.applicationinsights/2.22.0", "hashPath": "microsoft.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.AspNetCore/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-OuiZgRDX0zm3a1DRk/GT54ZsyTg8a88n3cpkVEYFJoRhT5X84l2C68BuKrglE0sIj+C0+o2WTR8S21YBD/ZWgA==", "path": "microsoft.applicationinsights.aspnetcore/2.22.0", "hashPath": "microsoft.applicationinsights.aspnetcore.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.DependencyCollector/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-gseSmuCshdZqcn5r6EW1Zx52e5/p2RpAsHSanlxs8pq+Pbg1RZP678tXtxfVuHC0fA3MVV852pnfFC7ZGB0jew==", "path": "microsoft.applicationinsights.dependencycollector/2.22.0", "hashPath": "microsoft.applicationinsights.dependencycollector.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.EventCounterCollector/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-/fXUyZIMwaWfETgire4fygaBhY8J+hXvTVhSFXKV0JOFBenzzU4smGW8iRUFhE534a3QrczuFfmfCCkXRKbsNg==", "path": "microsoft.applicationinsights.eventcountercollector/2.22.0", "hashPath": "microsoft.applicationinsights.eventcountercollector.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.PerfCounterCollector/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-nExsJsbN7694ueUNNBms/UNgza9WH4W/I6i5CnF9ujJ1sp57EL5Uk0NP9MDwlLVtYaaiznKPatVSv3Nu8vAplw==", "path": "microsoft.applicationinsights.perfcountercollector/2.22.0", "hashPath": "microsoft.applicationinsights.perfcountercollector.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.WindowsServer/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-9k1x1+Kq1fElvcv0o/w9w8tRWAa2Y0f4NPBeHF5b2xCety4GM1yv3K3Ra0lZwO3kW0SHlm9M8nrySuyKQlHyYA==", "path": "microsoft.applicationinsights.windowsserver/2.22.0", "hashPath": "microsoft.applicationinsights.windowsserver.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-Blb6S8UJSJ/jo6mxeO38gKgui75D2brp5NpXJoZUhyJzfmYsfhn7a4t5f+CDfAKyvie7sQB2FIzeEDQSiRE5zw==", "path": "microsoft.applicationinsights.windowsserver.telemetrychannel/2.22.0", "hashPath": "microsoft.applicationinsights.windowsserver.telemetrychannel.2.22.0.nupkg.sha512"}, "Microsoft.ApplicationInsights.WorkerService/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/KcaQf+Jy92vdHTd2P8zoaW/IIUl4VkzGkvmBqi1IFQ0JXR4f6LXB73/2GMGhWMc7+QMVHeqW0QDjbLU6Fw5g==", "path": "microsoft.applicationinsights.workerservice/2.21.0", "hashPath": "microsoft.applicationinsights.workerservice.2.21.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MqYc0DUxrhAPnb5b4HFspxsoJT+gJlLsliSxIgovf4BsbmpaXQId0/pDiVzLuEbmks2w1/lRfY8w0lQOuK1jQQ==", "path": "microsoft.aspnetcore.hosting/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-76cKcp2pWhvdV2TXTqMg/DyW7N6cDzTEhtL8vVWFShQN+Ylwv3eO/vUQr2BS3Hz4IZHEpL+FOo2T+MtymHDqDQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-+vD7HJYzAXNq17t+NgRkpS38cxuAyOBu8ixruOiA3nWsybozolUdALWiZ5QFtGRzajSLPFA2YsbO3NPcqoUwcw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.1.1", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.1.22": {"type": "package", "serviceable": true, "sha512": "sha512-+Blk++1JWqghbl8+3azQmKhiNZA5wAepL9dY2I6KVmu2Ri07MAcvAVC888qUvO7yd7xgRgZOMfihezKg14O/2A==", "path": "microsoft.aspnetcore.http/2.1.22", "hashPath": "microsoft.aspnetcore.http.2.1.22.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-ncAgV+cqsWSqjLXFUTyObGh4Tr7ShYYs3uW8Q/YpRwZn7eLV7dux5Z6GLY+rsdzmIHiia3Q2NWbLULQi7aziHw==", "path": "microsoft.aspnetcore.http.extensions/2.1.1", "hashPath": "microsoft.aspnetcore.http.extensions.2.1.1.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-klQdb/9+j0u8MDjoqHEgDCPz8GRhfsbRVvZIM3glFqjs8uY7S1hS9RvKZuz8o4dS9NsEpFp4Jccd8CQuIYHK0g==", "path": "microsoft.aspnetcore.jsonpatch/8.0.0", "hashPath": "microsoft.aspnetcore.jsonpatch.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/e5+eBvY759xiZJO+y1lHi4VzXqbDzTJSyCtKpaj3Ko2JAFQjiCOJ0ZHk2i4g4HpoSdXmzEXQsjr6dUX9U0/JA==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/8.0.0", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Testing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PWPgPAlRdMEkVCHHic/Yy6t37PsPsymY1WkEH0TnjTeIlYu+g8Qfw1axj/ZmNDLvhKdiUNC2E2thHrYf2s7Yhg==", "path": "microsoft.aspnetcore.mvc.testing/8.0.0", "hashPath": "microsoft.aspnetcore.mvc.testing.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.TestHost/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rk6Ai9bFf1KubVY5oEbEPN5fiKWW2oeU+easjokyUqqYyTHRsXlkjFeMvwecgoXsoTfXMSwEHzJp8FCjQcyYTQ==", "path": "microsoft.aspnetcore.testhost/8.0.0", "hashPath": "microsoft.aspnetcore.testhost.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-PGKIZt4+412Z/XPoSjvYu/QIbTxcAQuEFNoA1Pw8a9mgmO0ZhNBmfaNyhgXFf7Rq62kP0tT/2WXpxdcQhkFUPA==", "path": "microsoft.aspnetcore.webutilities/2.1.1", "hashPath": "microsoft.aspnetcore.webutilities.2.1.1.nupkg.sha512"}, "Microsoft.Azure.Amqp/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-Xf2mbwTSuUtqRlULKXCEuXPxlBhZzZXWmMxnxF64WJAelo3PA7kIDR4Bv+eOBYxHyr3FJtwG3/7rrhyXIx1Qzg==", "path": "microsoft.azure.amqp/2.6.4", "hashPath": "microsoft.azure.amqp.2.6.4.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.ApplicationInsights/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-0t3andm3vCeIAAlroO06GrgNmMTJzHZPSEpqIrLSnwc9zflg0/dBC7K3Ig+gA2AcM3Qo17K1zTvgokr9nlpUxw==", "path": "microsoft.azure.functions.worker.applicationinsights/1.1.0", "hashPath": "microsoft.azure.functions.worker.applicationinsights.1.1.0.nupkg.sha512"}, "Microsoft.Azure.Functions.Worker.Core/1.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-eeUM9EAbC0Bh8tpjKBbYjLuJVRr6Uyckvlz4PrPYvCVxnnq4qx5tcrzHSLj7F15xdhfaWtbwBcJzyvNXbe6mjg==", "path": "microsoft.azure.functions.worker.core/1.15.0", "hashPath": "microsoft.azure.functions.worker.core.1.15.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-KC8SXWbGIdoFVdlxKk9WHccm0llm9HypcHMLUUFabRiTS3SO2fQXNZfdiF3qkEdTJhbRrxhdRxjL4jbtwPq4Ew==", "path": "microsoft.codecoverage/17.8.0", "hashPath": "microsoft.codecoverage.17.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/pT9FOO0BxGSRscK/ekEb6TdiP3+nnyhPLElE1yuVG/QaZLaBAuM3RoywBHdIxWoFALaOS7ktXlKzuMX3khJ4A==", "path": "microsoft.entityframeworkcore.inmemory/8.0.0", "hashPath": "microsoft.entityframeworkcore.inmemory.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EJzSNO9oaAXnTdtdNO6npPRsIIeZCBSNmdQ091VDO7fBiOtJAAeEq6dtrVXIi3ZyjC5XRSAtVvF8SzcneRHqKQ==", "path": "microsoft.extensions.configuration.fileextensions/8.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5Ou6varcxLBzQ+Agfm0k0pnH7vrEITYlXMDuE6s7ZHlZHz6/G8XJ3iISZDr5rfwfge6RnXJ1+Wc479mMn52vjA==", "path": "microsoft.extensions.dependencymodel/8.0.1", "hashPath": "microsoft.extensions.dependencymodel.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-+tQeERLaSPA+G//SlIZ5pyv/jAmkn1xnMMOvFu3Bag3EJxwV4D9iEkHD2TaNiJOoFZ/VROUB76/H7n/75e9Dow==", "path": "microsoft.extensions.http.polly/6.0.9", "hashPath": "microsoft.extensions.http.polly.6.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-5OmXub+9MyX8FbqgO+hBJRHk1iJ+UZUU20oIU3wo+RbmH6Jtsja79rriHLlzlrkMzWbpCkCzF6f4Yb6iGbsDag==", "path": "microsoft.extensions.logging.applicationinsights/2.22.0", "hashPath": "microsoft.extensions.logging.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-SErON45qh4ogDp6lr6UvVmFYW0FERihW+IQ+2JyFv1PUyWktcJytFaWH5zarufJvZwhci7Rf1IyGXr9pVEadTw==", "path": "microsoft.extensions.objectpool/2.1.1", "hashPath": "microsoft.extensions.objectpool.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "path": "microsoft.identity.client/4.56.0", "hashPath": "microsoft.identity.client.4.56.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "path": "microsoft.identity.client.extensions.msal/4.56.0", "hashPath": "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-iI+9V+2ciCrbheeLjpmjcqCnhy+r6yCoEcid3nkoFWerHgjVuT6CPM4HODUTtUPe1uwks4wcnAujJ8u+IKogHQ==", "path": "microsoft.identitymodel.abstractions/6.22.0", "hashPath": "microsoft.identitymodel.abstractions.6.22.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lP<PERSON>phl8b2EuhOE9dMH6EZDmu7pS882O+HMi5BJNsigxHaWlBrYxZHFZgE18cyaPp6SSZcTkKkuzfjV/RRQKlA==", "path": "microsoft.net.http.headers/2.1.1", "hashPath": "microsoft.net.http.headers.2.1.1.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-BmTYGbD/YuDHmApIENdoyN1jCk0Rj1fJB0+B/fVekyTdVidr91IlzhqzytiUgaEAzL1ZJcYCme0MeBMYvJVzvw==", "path": "microsoft.net.test.sdk/17.8.0", "hashPath": "microsoft.net.test.sdk.17.8.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-TMBuzAHpTenGbGgk0SMTwyEkyijY/Eae4ZGsFNYJvAr/LDn1ku3Etp3FPxChmDp5HHF3kzJuoaa08N0xjqAJfQ==", "path": "microsoft.netcore.platforms/1.1.1", "hashPath": "microsoft.netcore.platforms.1.1.1.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-AYy6vlpGMfz5kOFq99L93RGbqftW/8eQTqjT9iGXW6s9MRP3UdtY8idJ8rJcjeSja8A18IhIro5YnH3uv1nz4g==", "path": "microsoft.testplatform.objectmodel/17.8.0", "hashPath": "microsoft.testplatform.objectmodel.17.8.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ivcl/7SGRmOT0YYrHQGohWiT5YCpkmy/UEzldfVisLm6QxbLaK3FAJqZXI34rnRLmqqDCeMQxKINwmKwAPiDw==", "path": "microsoft.testplatform.testhost/17.8.0", "hashPath": "microsoft.testplatform.testhost.17.8.0.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MongoDB.Bson/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQx/qtC2nu9oGiyNqAwfiDpUMweLi0nID677cyKykpwmj5AVMrnd//UwmcmuX95178DeY6rf7cjmA613TQXPiA==", "path": "mongodb.bson/2.25.0", "hashPath": "mongodb.bson.2.25.0.nupkg.sha512"}, "MongoDB.Driver/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMqnZTV6MuvoEI4yFtSvKJdAoN6NeyAEvG8aoxnrLIVd7bR84QxLgpsM1nhK17qkOcIx/IrpMIfrvp5iMnYGBg==", "path": "mongodb.driver/2.25.0", "hashPath": "mongodb.driver.2.25.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-oN4nLgO5HQEThTg/zqeoHqaO2+q64DBVb4r7BvhaFb0p0TM9jZKnCKvh1EA8d9E9swIz0CgvMrvL1mPyRCZzag==", "path": "mongodb.driver.core/2.25.0", "hashPath": "mongodb.driver.core.2.25.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-z/8JCULSHM1+mzkau0ivIkU9kIn8JEFFSkmYTSaMaWMMHt96JjUtMKuXxeGNGSnHZ5290ZPKIlQfjoWFk2sKog==", "path": "mongodb.libmongocrypt/1.8.2", "hashPath": "mongodb.libmongocrypt.1.8.2.nupkg.sha512"}, "Moq/4.20.70": {"type": "package", "serviceable": true, "sha512": "sha512-4rNnAwdpXJBuxqrOCzCyICXHSImOTRktCgCWXWykuF1qwoIsVvEnR7PjbMk/eLOxWvhmj5Kwt+kDV3RGUYcNwg==", "path": "moq/4.20.70", "hashPath": "moq.4.20.70.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Npgsql/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qiz74U+O7Mv4knrsXgKVYGJjgwoziK+aMFZqz7PtKR3vyGIhZA0tnW6HoUnL3X+YqtmVuhmoKkN8LDWEHMxPbw==", "path": "npgsql/8.0.0", "hashPath": "npgsql.8.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GDXiMS9peEdjSCU/rfgyHruio7q6tYuywGaktqEi6UPQ6ILechp3fVVX+dHXkIXt4nklCBzYVWkzFrSL9ubKUA==", "path": "npgsql.entityframeworkcore.postgresql/8.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.8.0.0.nupkg.sha512"}, "NuGet.Frameworks/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QWINE2x3MbTODsWT1Gh71GaGb5icBz4chS8VYvTgsBnsi8esgN6wtHhydd7fvToWECYGq7T4cgBBDiKD/363fg==", "path": "nuget.frameworks/6.5.0", "hashPath": "nuget.frameworks.6.5.0.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Quartz/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYg+O+yw24kNLqp1YyEaZCzpQyVhcLQ5vep61RDKPZFjRNMBfBxcZiHNRFMNuN0XQIQ6zTGTHdZl9h7CuEVvXA==", "path": "quartz/3.8.0", "hashPath": "quartz.3.8.0.nupkg.sha512"}, "Quartz.Extensions.DependencyInjection/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-p<PERSON><PERSON>26rqgTXTUdpA4TSRO6upn1YGcOKJhNw0WcIeD8ShLaO1lviM9KNE+Q5knIF4osdjOcTOsu7Rtjofth7olEw==", "path": "quartz.extensions.dependencyinjection/3.8.0", "hashPath": "quartz.extensions.dependencyinjection.3.8.0.nupkg.sha512"}, "Quartz.Extensions.Hosting/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-cNSrx0ifPTRL6AVXPTTXS6Yzv8Zc6pq0GXtdOOFfBAuWBmBRzZy1XZLl5ylOIWNFW3BHbwHOXm4NJ85WT/LKfw==", "path": "quartz.extensions.hosting/3.8.0", "hashPath": "quartz.extensions.hosting.3.8.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SendGrid/9.28.1": {"type": "package", "serviceable": true, "sha512": "sha512-LyIkgjd+svXuQxpqe5pvyOccyUdKcDqwnBNDPjyCngkKeVpXAOTAr3U1DBLWqHEbFHvu2UBFki3SJzDwxvJdfA==", "path": "sendgrid/9.28.1", "hashPath": "sendgrid.9.28.1.nupkg.sha512"}, "SendGrid.Extensions.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-M3dHAkRIIDWvGNro5S25xjQ+nvUTomZ5er12TL0Re+G2UwIntMvO2OthECb3SV28AvOtDd4yZERjdHTrJ+gD1w==", "path": "sendgrid.extensions.dependencyinjection/1.0.1", "hashPath": "sendgrid.extensions.dependencyinjection.1.0.1.nupkg.sha512"}, "Serilog/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2jDkUrSh5EofOp7Lx5Zgy0EB+7hXjjxE2ktTb1WVQmU00lDACR2TdROGKU0K1pDTBSJBN1PqgYpgOZF8mL7NJw==", "path": "serilog/4.0.0", "hashPath": "serilog.4.0.0.nupkg.sha512"}, "Serilog.AspNetCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "path": "serilog.aspnetcore/6.0.1", "hashPath": "serilog.aspnetcore.6.0.1.nupkg.sha512"}, "Serilog.Enrichers.GlobalLogContext/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IIZcj5mAUVhIl/NTA+YI2KC+sPDzcwvs0ZMHH42jsPfl1a4LVX7ohVpw5UK+e3GxuV3Nv239Il5oM2peUIl44g==", "path": "serilog.enrichers.globallogcontext/3.0.0", "hashPath": "serilog.enrichers.globallogcontext.3.0.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Formatting.Compact/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ob6z3ikzFM3D1xalhFuBIK1IOWf+XrQq+H4KeH4VqBcPpNcmUgZlRQ2h3Q7wvthpdZBBoY86qZOI2LCXNaLlNA==", "path": "serilog.formatting.compact/2.0.0", "hashPath": "serilog.formatting.compact.2.0.0.nupkg.sha512"}, "Serilog.Settings.Configuration/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hn8HCAmupon7N0to20EwGeNJ+L3iRzjGzAHIl8+8CCFlEkVedHvS6NMYMb0VPNMsDgDwOj4cPBPV6Fc2hb0/7w==", "path": "serilog.settings.configuration/8.0.2", "hashPath": "serilog.settings.configuration.8.0.2.nupkg.sha512"}, "Serilog.Sinks.ApplicationInsights/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AlYq1JFqh+RFKwLKZ3X224Zbe1gnkMbqSELp2FApLN0iMyRPdwwxMJBCCrk49C8qOefBd4zN+J/1Tq3i75DunA==", "path": "serilog.sinks.applicationinsights/4.0.0", "hashPath": "serilog.sinks.applicationinsights.4.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHyl2/93Roymf2eudPl/6Eeu2GQ93Ucy4GM1UPF0jyd7CIW8r+Bk5ohdbjjyjB9TQSpP2ovOuj6ltf9DjoWHtg==", "path": "serilog.sinks.http/8.0.0", "hashPath": "serilog.sinks.http.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Logz.Io/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-uZN5FWMvpk0m/6u7PiKShODbUvfxQZGzK+D91BaxV1ePVJSE0xaqJwCh7rp8/mhPp2C0UPmXOt1hAF306Fzn1Q==", "path": "serilog.sinks.logz.io/7.1.0", "hashPath": "serilog.sinks.logz.io.7.1.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "Serilog.Sinks.Seq/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LtxlH5xE3ZPxmCYL5+I8tPzytnR91xfFFIIUIcpoGq69a45eyFkrVMonApww+B08a0I++GfM7jP1oB6GBhOR1w==", "path": "serilog.sinks.seq/6.0.0", "hashPath": "serilog.sinks.seq.6.0.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "Shouldly/4.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-dKAKiSuhLKqD2TXwLKtqNg1nwzJcIKOOMncZjk9LYe4W+h+SCftpWdxwR79YZUIHMH+3Vu9s0s0UHNrgICLwRQ==", "path": "shouldly/4.2.1", "hashPath": "shouldly.4.2.1.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "starkbank-ecdsa/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog==", "path": "starkbank-ecdsa/1.3.3", "hashPath": "starkbank-ecdsa.1.3.3.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Annotations/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TANd+yRHl99IPUWGcifwP+FbK48Lx7rrTqd21z/7GaduUdiKFF7/uUs85dC8Tdll70erb2s1eDsvItnt83Qz2A==", "path": "swashbuckle.aspnetcore.annotations/9.0.1", "hashPath": "swashbuckle.aspnetcore.annotations.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4/9tBsbeCN+ewwqjEBPq3BszNE9tOvA1iDogwT7qW7L0Uh942IozhtF9VaICD70XyZIlKNiHjc2Vt5QE09P4nw==", "path": "swashbuckle.aspnetcore.swagger/9.0.1", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Za5w1NfLMF0Tt4GTgC491wfmuuMwNw8A5nwZuytpBd4UXYZxkwNJa3QnRYGbUQD9MjLzQCYnKc8rMrr5LglW6A==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "path": "system.clientmodel/1.0.0", "hashPath": "system.clientmodel.1.0.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "path": "system.diagnostics.performancecounter/6.0.0", "hashPath": "system.diagnostics.performancecounter.6.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7OkTRIjqIjAKu6ef/fuj8ynCZTPcTYZnvHaq48bniACgXXJogmEoIc56YCDNTc14xhsbLmgpS3KP+evbsUa2g==", "path": "system.io.packaging/6.0.0", "hashPath": "system.io.packaging.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Management/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-10J1D0h/lioojphfJ4Fuh5ZUThT/xOVHdV9roGBittKKNP2PMjrvibEdbVTGZcPra1399Ja3tqIJLyQrc5Wmhg==", "path": "system.management/6.0.1", "hashPath": "system.management.6.0.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "path": "system.text.json/8.0.4", "hashPath": "system.text.json.8.0.4.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "TinyHelpers/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Fg5gvCWBqB40ba33PMK831OQAdpN7Ny2+QWh7lxdfk47vbUtxncKJiC6fhRz/NxmsNp8gWHdRh187TscXAIhPA==", "path": "tinyhelpers/3.0.2", "hashPath": "tinyhelpers.3.0.2.nupkg.sha512"}, "TinyHelpers.EntityFrameworkCore/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JioI+R+zijUZxy6Utbo5/KVM9E88BjSim8PhsbMNAlQHNPigxhGqfGGR23uLzvGPS67lyDTesBXaUimqN7+v8A==", "path": "tinyhelpers.entityframeworkcore/3.0.2", "hashPath": "tinyhelpers.entityframeworkcore.3.0.2.nupkg.sha512"}, "XLParser/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-8tnUCmMbn6c/HGuHFyq1sulHym8YReTKDNXFFQk0U8nldNx6RGrwRvLh0+aQjEc7YoC3Q1ztH1npLt7dClKqjw==", "path": "xlparser/1.5.2", "hashPath": "xlparser.1.5.2.nupkg.sha512"}, "xunit/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-7LhFS2N9Z6Xgg8aE5lY95cneYivRMfRI8v+4PATa4S64D5Z/Plkg0qa8dTRHSiGRgVZ/CL2gEfJDE5AUhOX+2Q==", "path": "xunit/2.9.2", "hashPath": "xunit.2.9.2.nupkg.sha512"}, "xunit.abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "path": "xunit.abstractions/2.0.3", "hashPath": "xunit.abstractions.2.0.3.nupkg.sha512"}, "xunit.analyzers/1.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-hptYM7vGr46GUIgZt21YHO4rfuBAQS2eINbFo16CV/Dqq+24Tp+P5gDCACu1AbFfW4Sp/WRfDPSK8fmUUb8s0Q==", "path": "xunit.analyzers/1.16.0", "hashPath": "xunit.analyzers.1.16.0.nupkg.sha512"}, "xunit.assert/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-QkNBAQG4pa66cholm28AxijBjrmki98/vsEh4Sx5iplzotvPgpiotcxqJQMRC8d7RV7nIT8ozh97957hDnZwsQ==", "path": "xunit.assert/2.9.2", "hashPath": "xunit.assert.2.9.2.nupkg.sha512"}, "xunit.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-O6RrNSdmZ0xgEn5kT927PNwog5vxTtKrWMihhhrT0Sg9jQ7iBDciYOwzBgP2krBEk5/GBXI18R1lKvmnxGcb4w==", "path": "xunit.core/2.9.2", "hashPath": "xunit.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ol+KlBJz1x8BrdnhN2DeOuLrr1I/cTwtHCggL9BvYqFuVd/TUSzxNT5O0NxCIXth30bsKxgMfdqLTcORtM52yQ==", "path": "xunit.extensibility.core/2.9.2", "hashPath": "xunit.extensibility.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.execution/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-rKMpq4GsIUIJibXuZoZ8lYp5EpROlnYaRpwu9Zr0sRZXE7JqJfEEbCsUriZqB+ByXCLFBJyjkTRULMdC+U566g==", "path": "xunit.extensibility.execution/2.9.2", "hashPath": "xunit.extensibility.execution.2.9.2.nupkg.sha512"}, "xunit.runner.visualstudio/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HggUqjQJe8PtDxcP25Q+CnR6Lz4oX3GElhD9V4oU2+75x9HI6A6sxbfKGS4UwU4t4yJaS9fBmAuriz8bQApNjw==", "path": "xunit.runner.visualstudio/3.0.0", "hashPath": "xunit.runner.visualstudio.3.0.0.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "BlueTape.DataAccess.Mongo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.OBS.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.ARS/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.ARS.DataAccess/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.ARS.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.DataAccess.External/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.API/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.AutoPay/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.DataAccess/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.DataAccess.Company/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.DataAccess.SOFR/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.Infrastructure.Hosting/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.MonitoringService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.UnitOfWork/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.Reporting/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.Reporting.DataAccess/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.Reporting.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/1*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}