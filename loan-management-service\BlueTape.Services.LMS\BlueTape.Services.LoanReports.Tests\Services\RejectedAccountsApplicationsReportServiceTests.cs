using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.DTOs.CreditApplication;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Options;
using BlueTape.Services.Reporting.Models.AccountApplicationsReport;
using BlueTape.Services.Reporting.Models.LoanTapeReports;
using BlueTape.Services.Reporting.Models.ReportFileGeneration;
using BlueTape.Services.Reporting.Services;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Shouldly;

namespace BlueTape.Services.LoanReports.Tests.Services;

public class RejectedAccountsApplicationsReportServiceTests
{
    private readonly Mock<IRejectedAccountsApplicationsReportRowDataBuilder> _rowDataBuilderMock;
    private readonly Mock<IOnBoardingIntegrationExternalService> _onBoardingApiMock;
    private readonly Mock<IReportsUploadService> _reportsUploadServiceMock;
    private readonly Mock<IReportFileGenerationService> _reportFileGenerationServiceMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IReportsNotificationsService> _reportsNotificationsServiceMock;
    private readonly Mock<ILogger<ManualPaymentsReportService>> _loggerMock;
    private readonly RejectedAccountsApplicationsReportService _service;
    private readonly IOptions<AccountApplicationsRejectReportOptions> _options;

    public RejectedAccountsApplicationsReportServiceTests()
    {
        _rowDataBuilderMock = new Mock<IRejectedAccountsApplicationsReportRowDataBuilder>();
        _onBoardingApiMock = new Mock<IOnBoardingIntegrationExternalService>();
        _reportsUploadServiceMock = new Mock<IReportsUploadService>();
        _reportFileGenerationServiceMock = new Mock<IReportFileGenerationService>();
        _dateProviderMock = new Mock<IDateProvider>();
        _reportsNotificationsServiceMock = new Mock<IReportsNotificationsService>();
        _loggerMock = new Mock<ILogger<ManualPaymentsReportService>>();
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2024, 1, 1));
        //add options mock
        _options = Options.Create(new AccountApplicationsRejectReportOptions()
        {
            EnableEmailNotification = false,
            RejectionReasons = new Dictionary<string, string>()
            {
                { "L01", "Business is too recent" },
                { "L02", "Revenue is too low" },
                { "L03", "Business cash flow positions insufficient" },
                { "L04", "Unwilling to link bank account" },
                { "L05", "Unable to verify the business" },
                { "L06", "Unable to KYC co-owners" },
                { "L07", "One or more co-owners have bankruptcy filings" },
                { "L08", "One or more co-owners has insufficient credit (Fico<630)" },
            }
        });

        _service = new RejectedAccountsApplicationsReportService(
            _rowDataBuilderMock.Object,
            _onBoardingApiMock.Object,
            _reportsUploadServiceMock.Object,
            _reportFileGenerationServiceMock.Object,
            _dateProviderMock.Object,
            _reportsNotificationsServiceMock.Object,
            _options,
            _loggerMock.Object);
    }

    [Fact]
    public async Task GenerateReport_NoRejectedApplications_ReturnsEmptyReport()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        _onBoardingApiMock
            .Setup(x => x.GetCreditApplications(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                "rejected",
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((List<CreditApplicationDto>)null);

        var expectedFileName = "RejectedAccountsApplications";
        var reportFile = new ReportFile
        {
            FileName = expectedFileName,
            FileBinary = new byte[] { 1, 2, 3 }
        };

        _reportFileGenerationServiceMock
            .Setup(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedAccountsApplicationsReportModel>(),
                It.IsAny<string>()))
            .ReturnsAsync(reportFile);

        // Act
        var result = await _service.GenerateReport(CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ReportDate.ShouldBe(currentDate);
        result.Rows.ShouldBeEmpty();
        result.FileName.ShouldBe(expectedFileName);

        VerifyMockCalls();
    }

    [Fact]
    public async Task GenerateReport_WithRejectedApplications_GeneratesAndUploadsReport()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var applications = new List<CreditApplicationDto>
        {
            new() { Id = "app1" },
            new() { Id = "app2" }
        };

        var reportRows = new List<RejectedAccountsApplicationsReportRowModel>
        {
            new() { ApplicationId = "app1" },
            new() { ApplicationId = "app2" }
        };

        _onBoardingApiMock
            .Setup(x => x.GetCreditApplications(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                "rejected",
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(applications);

        _rowDataBuilderMock
            .Setup(x => x.Build(
                It.IsAny<IReadOnlyCollection<CreditApplicationDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(reportRows);

        var expectedFileName = "RejectedAccountsApplications";
        var reportFile = new ReportFile
        {
            FileName = expectedFileName,
            FileBinary = new byte[] { 1, 2, 3 }
        };

        _reportFileGenerationServiceMock
            .Setup(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedAccountsApplicationsReportModel>(),
                It.IsAny<string>()))
            .ReturnsAsync(reportFile);

        // Act
        var result = await _service.GenerateReport(CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ReportDate.ShouldBe(currentDate);
        result.Rows.Count.ShouldBe(2);
        result.FileName.ShouldBe(expectedFileName);

        VerifyMockCalls();
    }

    private void VerifyMockCalls()
    {
        _onBoardingApiMock.Verify(
            x => x.GetCreditApplications(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                "rejected",
                It.IsAny<CancellationToken>()),
            Times.Once);

        _reportFileGenerationServiceMock.Verify(
            x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedAccountsApplicationsReportModel>(),
                It.IsAny<string>()),
            Times.Once);

        _reportsUploadServiceMock.Verify(
            x => x.UploadLoanTapeToS3(
                It.IsAny<byte[]>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GenerateReport_WithEmailNotificationEnabled_SendsEmail()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);
        _options.Value.EnableEmailNotification = true;
        var applications = new List<CreditApplicationDto>
        {
            new() { Id = "app1" }
        };

        _onBoardingApiMock
            .Setup(x => x.GetCreditApplications(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                "rejected",
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(applications);

        _rowDataBuilderMock.Setup(x => x.Build(
                It.IsAny<IReadOnlyCollection<CreditApplicationDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<RejectedAccountsApplicationsReportRowModel>
            {
                new() { ApplicationId = "app1" }
            });

        var reportFile = new ReportFile
        {
            FileName = "RejectedAccountsApplications",
            FileBinary = [1, 2, 3]

        };

        _reportFileGenerationServiceMock
            .Setup(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedAccountsApplicationsReportModel>(),
                It.IsAny<string>()))
            .ReturnsAsync(reportFile);

        // Act
        var result = await _service.GenerateReport(CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        VerifyMockCallsForEmailSending();
    }

    private void VerifyMockCallsForEmailSending()
    {
        _reportsNotificationsServiceMock.Verify(
            x => x.SendLoanTapeReportGeneratedNotification(
                It.IsAny<GeneratedLoanTapeReportModel>(),
                FundingSource.Arcadia,
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}