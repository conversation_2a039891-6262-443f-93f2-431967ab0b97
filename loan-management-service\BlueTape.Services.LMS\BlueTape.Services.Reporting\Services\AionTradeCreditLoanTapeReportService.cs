﻿using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Constants;
using BlueTape.Services.Reporting.Domain.Enums;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;


namespace BlueTape.Services.Reporting.Services;

public class AionTradeCreditLoanTapeReportService(
    ILoanTapeReportRowDataBuilder rowDataBuilder,
    IHeaderDataBuilder headerDataBuilder,
    ILoanReportsHistoryService loanReportsHistoryService,
    ILoanRepository loanRepository,
    IReportsUploadService reportsUploadService,
    IReportFileGenerationService reportFileGenerationService,
    IReportsNotificationsService reportsNotificationsService,
    IDateProvider dateProvider,
    ILogger<AionTradeCreditLoanTapeReportService> logger)
    : TradeCreditLoanTapeReportServiceBase(
        rowData<PERSON>uilder,
        headerDataBuilder,
        loanReportsHistoryService,
        loanRepository,
        reportsUploadService,
        reportFileGenerationService,
        reportsNotificationsService,
        dateProvider,
        logger), IAionTradeCreditLoanTapeReportService
{
    protected override string NameOfReport => "TradeCredit Loan Tape Report";
    protected override ReportType ReportType => ReportType.LoanTapeAionLoanSales;
    protected override string ReportFileNamePrefix => LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix;
    protected override string BankName => LoanTapeReportConstants.AionBankName;
    protected override string BankAccountKeyKeyName => LoanTapeReportConstants.AionFundingBankAccountKeyName;
    protected override PaymentProvider LoanPaymentProvider => PaymentProvider.Aion;
}