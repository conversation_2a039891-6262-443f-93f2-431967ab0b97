﻿using BlueTape.Services.LMS.Application.Tests.Attributes;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Constants;
using BlueTape.Services.Reporting.Domain.Enums;
using BlueTape.Services.Reporting.Models.LoanReportsHistory;
using BlueTape.Services.Reporting.Models.LoanTapeReports;
using BlueTape.Services.Reporting.Models.ReportFileGeneration;
using BlueTape.Services.Reporting.Models.TradeCreditReports;
using BlueTape.Services.Reporting.Services;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using Shouldly;
using System.Linq.Expressions;

namespace BlueTape.Services.LoanReports.Tests.Services
{
    public class AionTradeCreditLoanTapeReportServiceTests
    {
        private readonly AionTradeCreditLoanTapeReportService _service;

        private readonly Mock<ILoanTapeReportRowDataBuilder> _rowDataBuilderMock = new();
        private readonly Mock<IHeaderDataBuilder> _headerDataBuilderMock = new();
        private readonly Mock<ILoanReportsHistoryService> _loanReportsHistoryServiceMock = new();
        private readonly Mock<ILoanRepository> _loanRepositoryMock = new();
        private readonly Mock<IReportsUploadService> _reportsUploadServiceMock = new();
        private readonly Mock<IReportFileGenerationService> _reportFileGenerationServiceMock = new();
        private readonly Mock<IReportsNotificationsService> _reportsNotificationsServiceMock = new();
        private readonly Mock<ILogger<AionTradeCreditLoanTapeReportService>> _loggerMock = new();
        private readonly Mock<IDateProvider> _dateProviderMock = new();
        private readonly DateOnly _currentDate = DateOnly.FromDateTime(DateTime.UtcNow);

        public AionTradeCreditLoanTapeReportServiceTests()
        {

            _dateProviderMock.Setup(x => x.CurrentDate).Returns(_currentDate);

            _service = new AionTradeCreditLoanTapeReportService(
                _rowDataBuilderMock.Object,
                _headerDataBuilderMock.Object,
                _loanReportsHistoryServiceMock.Object,
                _loanRepositoryMock.Object,
                _reportsUploadServiceMock.Object,
                _reportFileGenerationServiceMock.Object,
                _reportsNotificationsServiceMock.Object,
                _dateProviderMock.Object,
                _loggerMock.Object);
        }

        [Theory]
        [InlineData(FundingSource.Arcadia)]
        [InlineData(FundingSource.Raistone)]
        [InlineData(FundingSource.Aion)]
        public async Task GenerateReport_NoApplicableLoansAvailable_ReturnsEmptyReportModel(FundingSource fundingSource)
        {
            // Arrange
            _loanReportsHistoryServiceMock.Setup(x => x.GetByReportType(ReportType.LoanTapeAionLoanSales, default))
                .ReturnsAsync(new List<LoanReportsHistory>());
            _loanReportsHistoryServiceMock.Setup(x => x.Insert(It.IsAny<CreateLoanReportsHistoryModel>(), default))
                .ReturnsAsync(new LoanReportsHistory
                {
                    Id = Guid.NewGuid()
                });
            _loanRepositoryMock.Setup(x =>
                    x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0,
                        int.MaxValue))
                .ReturnsAsync(new List<LoanEntity>());
            _reportFileGenerationServiceMock.Setup(x => x.GenerateXlsReportFilesAsync(It.IsAny<CreateTradeCreditReportModel>(),
                    It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix))))
                .ReturnsAsync(new ReportFile { FileName = $"{LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix}_2024-09-23.xlsx" });
            _reportFileGenerationServiceMock.Setup(x => x.GenerateCsvReportFilesAsync(It.IsAny<CreateTradeCreditReportModel>(),
                    It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix))))
                .ReturnsAsync(new ReportFile { FileName = $"{LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix}_2024-09-23.csv" });

            // Act
            var result = await _service.GenerateReport(fundingSource, default);

            // Assert
            result.ShouldNotBeNull();
            result.Rows.ShouldBeEmpty();
            result.HeaderData.TotalAdvance.ShouldBeNull();

            _loanReportsHistoryServiceMock.Verify(x => x.GetByReportType(ReportType.LoanTapeAionLoanSales, default),
                Times.Once);
            _loanRepositoryMock.Verify(x =>
                    x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0,
                        int.MaxValue),
                Times.Once);
        }

        [Theory, AutoDataWithDateOnly]
        public async Task GenerateReport_ApplicableLoansExist_GeneratesReportData(
            List<LoanEntity> loans,
            List<TradeCreditReportRowModel> rowsData,
            TradeCreditReportHeaderData headerData,
            FundingSource fundingSource)
        {
            // Arrange
            var loanIds = loans.Select(x => x.Id).ToList();
            var reportFileName = $"{LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix}_2024-09-23.csv";
            var loansForReportIds = loanIds.Skip(1).Take(2).ToList();

            _loanReportsHistoryServiceMock.Setup(x => x.GetByReportType(ReportType.LoanTapeAionLoanSales, default))
                .ReturnsAsync(new List<LoanReportsHistory>());
            _loanRepositoryMock
                .Setup(x => x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0,
                    int.MaxValue))
                .ReturnsAsync(loans);
            _rowDataBuilderMock.Setup(x => x.BuildTradeCreditReportRowsData(It.Is<IReadOnlyCollection<LoanEntity>>(
            x => x.Select(loan => loan.Id).SequenceEqual(loansForReportIds)), default)).ReturnsAsync(rowsData);
            _headerDataBuilderMock.Setup(x => x.BuildForTradeCredit(rowsData, LoanTapeReportConstants.AionFundingBankAccountKeyName, _currentDate))
                .Returns(headerData);
            _loanReportsHistoryServiceMock.Setup(x => x.Insert(It.Is<CreateLoanReportsHistoryModel>(
                    model => model.LoanReportsHistoryItems.Select(item => item.LoanId).SequenceEqual(loansForReportIds)
                             && model.Report == ReportType.LoanTapeAionLoanSales), default))
                .ReturnsAsync(new LoanReportsHistory { Id = Guid.NewGuid() });
            _reportFileGenerationServiceMock.Setup(x => x.GenerateCsvReportFilesAsync(
                    It.IsAny<CreateTradeCreditReportModel>(),
                    It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix))))
                .ReturnsAsync(new ReportFile { FileName = reportFileName });
            _reportFileGenerationServiceMock.Setup(x => x.GenerateXlsReportFilesAsync(
                    It.IsAny<CreateTradeCreditReportModel>(),
                    It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix))))
                .ReturnsAsync(new ReportFile { FileName = reportFileName.Replace(".csv", ".xlsx") });
            _loanRepositoryMock.Setup(x => x.GetLoanRelations(It.IsAny<Expression<Func<LoanRelationsEntity, bool>>>(), default)).ReturnsAsync(new List<LoanRelationsEntity>()
            {
                new()
                {
                    ChildLoanId = loans[0].Id
                }
            });

            // Act
            var result = await _service.GenerateReport(fundingSource, default);

            // Assert
            result.ShouldNotBeNull();
            result.Rows.ShouldBe(rowsData);
            result.HeaderData.ShouldBe(headerData);

            _loanReportsHistoryServiceMock.Verify(x => x.GetByReportType(ReportType.LoanTapeAionLoanSales, default),
                Times.Once);
            _rowDataBuilderMock.Verify(x => x.BuildTradeCreditReportRowsData(It.Is<IReadOnlyCollection<LoanEntity>>(x => x.Select(loan => loan.Id).SequenceEqual(loansForReportIds)), default), Times.Once);
            _headerDataBuilderMock.Verify(x => x.BuildForTradeCredit(rowsData, LoanTapeReportConstants.AionFundingBankAccountKeyName, _currentDate), Times.Once);
            _loanRepositoryMock.Verify(
                x => x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0,
                    int.MaxValue), Times.Once);
            _reportsUploadServiceMock.Verify(
                x => x.UploadLoanTapeToS3(It.IsAny<byte[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
                Times.Once);
            _reportFileGenerationServiceMock.Verify(x => x.GenerateCsvReportFilesAsync(
                It.IsAny<CreateTradeCreditReportModel>(),
                It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix))), Times.Once);
            _reportFileGenerationServiceMock.Verify(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateTradeCreditReportModel>(),
                It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.AionLoanSalesReportFileNamePrefix))), Times.Once);
            _reportsNotificationsServiceMock.Verify(x => x.SendLoanTapeReportGeneratedNotification(
                    It.Is<GeneratedLoanTapeReportModel>(m => m.TotalAmount == headerData.TotalAdvance
                                                             && m.FileName.StartsWith(LoanTapeReportConstants
                                                                 .AionLoanSalesReportFileNamePrefix)
                                                             && m.ReportDate == headerData.ReportDate), FundingSource.Arcadia, default), Times.Once);
            _loanRepositoryMock.Verify(x => x.GetLoanRelations(It.IsAny<Expression<Func<LoanRelationsEntity, bool>>>(), default), Times.Once);
        }
    }
}