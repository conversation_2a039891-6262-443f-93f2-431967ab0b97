using BlueTape.OBS.Client.Abstractions;
using BlueTape.OBS.DTOs;
using BlueTape.OBS.DTOs.DrawApproval.Queries;
using BlueTape.OBS.DTOs.DrawApproval.Responses;
using BlueTape.OBS.Enums;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Options;
using BlueTape.Services.Reporting.Models.LoanTapeReports;
using BlueTape.Services.Reporting.Models.RejectedInvoiceDraws;
using BlueTape.Services.Reporting.Models.ReportFileGeneration;
using BlueTape.Services.Reporting.Services;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Shouldly;

namespace BlueTape.Services.LoanReports.Tests.Services;

public class RejectedInvoiceDrawsReportServiceTests
{
    private readonly Mock<IRejectedInvoiceDrawsReportRowDataBuilder> _rowDataBuilderMock;
    private readonly Mock<IOnBoardingIntegrationExternalService> _onBoardingApiMock;
    private readonly Mock<IReportsUploadService> _reportsUploadServiceMock;
    private readonly Mock<IReportFileGenerationService> _reportFileGenerationServiceMock;
    private readonly Mock<IDateProvider> _dateProviderMock;
    private readonly Mock<IReportsNotificationsService> _reportsNotificationsServiceMock;
    private readonly RejectedInvoiceDrawsReportService _service;
    private readonly IOptions<InvoiceDrawsRejectReportOptions> _options;

    public RejectedInvoiceDrawsReportServiceTests()
    {
        _rowDataBuilderMock = new Mock<IRejectedInvoiceDrawsReportRowDataBuilder>();
        _onBoardingApiMock = new Mock<IOnBoardingIntegrationExternalService>();
        _reportsUploadServiceMock = new Mock<IReportsUploadService>();
        _reportFileGenerationServiceMock = new Mock<IReportFileGenerationService>();
        _dateProviderMock = new Mock<IDateProvider>();
        _reportsNotificationsServiceMock = new Mock<IReportsNotificationsService>();
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(new DateOnly(2024, 1, 1));
        var loggerMock = new Mock<ILogger<RejectedInvoiceDrawsReportService>>();
        //add options mock
        _options = Options.Create(new InvoiceDrawsRejectReportOptions()
        {
            EnableEmailNotification = false,
        });

        _service = new RejectedInvoiceDrawsReportService(
            _rowDataBuilderMock.Object,
            _onBoardingApiMock.Object,
            _reportsUploadServiceMock.Object,
            _reportFileGenerationServiceMock.Object,
            _dateProviderMock.Object,
            _reportsNotificationsServiceMock.Object,
            _options,
            loggerMock.Object);
    }

    [Fact]
    public async Task GenerateReport_NoRejectedDraws_ReturnsEmptyReport()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        _onBoardingApiMock
            .Setup(x => x.GetDrawApprovalList(
                It.Is<GetDrawApprovalsQueryWithPagination>(q =>
                    q.Status!.Contains(DrawApprovalStatus.Rejected.ToString()) &&
                    q.PageSize == int.MaxValue),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new GetQueryWithPaginationResultDto<DrawApprovalDto>
            {
                Result = new List<DrawApprovalDto>(),
                TotalCount = 0
            });

        var expectedFileName = "RejectedInvoiceDraws";
        var reportFile = new ReportFile
        {
            FileName = expectedFileName,
            FileBinary = [1, 2, 3]
        };

        _reportFileGenerationServiceMock
            .Setup(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedInvoiceDrawsReportModel>(),
                It.IsAny<string>()))
            .ReturnsAsync(reportFile);

        // Act
        var result = await _service.GenerateReport(CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ReportDate.ShouldBe(currentDate);
        result.Rows.ShouldBeEmpty();
        result.FileName.ShouldBe(expectedFileName);

        VerifyMockCalls();
    }

    [Fact]
    public async Task GenerateReport_WithRejectedDraws_GeneratesAndUploadsReport()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var draws = new List<DrawApprovalDto>
        {
            new() { Id = "draw1", Status = DrawApprovalStatus.Rejected },
            new() { Id = "draw2", Status = DrawApprovalStatus.Rejected }
        };

        var reportRows = new List<RejectedInvoiceDrawsReportRowModel>
        {
            new() { ApplicationId = "draw1" },
            new() { ApplicationId = "draw2" }
        };

        _onBoardingApiMock
            .Setup(x => x.GetDrawApprovalList(
                It.Is<GetDrawApprovalsQueryWithPagination>(q =>
                    q.Status!.Contains(DrawApprovalStatus.Rejected.ToString()) &&
                    q.PageSize == int.MaxValue),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new GetQueryWithPaginationResultDto<DrawApprovalDto>
            {
                Result = draws,
                TotalCount = draws.Count
            });

        _rowDataBuilderMock
            .Setup(x => x.Build(
                It.IsAny<IReadOnlyCollection<DrawApprovalDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(reportRows);

        var expectedFileName = "RejectedInvoiceDraws";
        var reportFile = new ReportFile
        {
            FileName = expectedFileName,
            FileBinary = [1, 2, 3]
        };

        _reportFileGenerationServiceMock
            .Setup(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedInvoiceDrawsReportModel>(),
                It.IsAny<string>()))
            .ReturnsAsync(reportFile);

        // Act
        var result = await _service.GenerateReport(CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ReportDate.ShouldBe(currentDate);
        result.Rows.Count.ShouldBe(2);
        result.FileName.ShouldBe(expectedFileName);

        VerifyMockCalls();
    }

    private void VerifyMockCalls()
    {
        _onBoardingApiMock.Verify(
            x => x.GetDrawApprovalList(
                It.Is<GetDrawApprovalsQueryWithPagination>(q =>
                    q.Status!.Contains(DrawApprovalStatus.Rejected.ToString()) &&
                    q.PageSize == int.MaxValue),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _reportFileGenerationServiceMock.Verify(
            x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedInvoiceDrawsReportModel>(),
                It.IsAny<string>()),
            Times.Once);

        _reportsUploadServiceMock.Verify(
            x => x.UploadLoanTapeToS3(
                It.IsAny<byte[]>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GenerateReport_WithEmailNotificationEnabled_SendsEmail()
    {
        // Arrange
        var currentDate = new DateOnly(2024, 1, 1);
        _dateProviderMock.Setup(x => x.CurrentDate).Returns(currentDate);

        var draws = new List<DrawApprovalDto>
        {
            new() { Id = "draw1", Status = DrawApprovalStatus.Rejected }
        };
        _options.Value.EnableEmailNotification = true;

        _onBoardingApiMock
            .Setup(x => x.GetDrawApprovalList(
                It.Is<GetDrawApprovalsQueryWithPagination>(q =>
                    q.Status!.Contains(DrawApprovalStatus.Rejected.ToString()) &&
                    q.PageSize == int.MaxValue),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new GetQueryWithPaginationResultDto<DrawApprovalDto>
            {
                Result = draws,
                TotalCount = draws.Count
            });

        _rowDataBuilderMock
            .Setup(x => x.Build(
                It.IsAny<IReadOnlyCollection<DrawApprovalDto>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<RejectedInvoiceDrawsReportRowModel>
            {
                new() { ApplicationId = "draw1" }
            });

        var reportFile = new ReportFile
        {
            FileName = "RejectedInvoiceDraws",
            FileBinary = [1, 2, 3]
        };

        _reportFileGenerationServiceMock
            .Setup(x => x.GenerateXlsReportFilesAsync(
                It.IsAny<CreateRejectedInvoiceDrawsReportModel>(),
                It.IsAny<string>()))
            .ReturnsAsync(reportFile);

        // Act
        var result = await _service.GenerateReport(CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        VerifyEmailNotificationSent();
    }

    private void VerifyEmailNotificationSent()
    {
        _reportsNotificationsServiceMock.Verify(
            x => x.SendLoanTapeReportGeneratedNotification(
                It.IsAny<GeneratedLoanTapeReportModel>(),
                FundingSource.Arcadia,
                It.IsAny<CancellationToken>()),
            Times.Never);
    }
}