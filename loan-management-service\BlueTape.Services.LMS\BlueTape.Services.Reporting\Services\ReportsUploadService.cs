﻿using Amazon;
using BlueTape.AWSS3;
using BlueTape.AWSS3.Abstractions;
using BlueTape.Services.LMS.Infrastructure;
using BlueTape.Services.LMS.Infrastructure.Exceptions;
using BlueTape.Services.LMS.Infrastructure.Options;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueTape.Services.Reporting.Services;

public class ReportsUploadService(
    IDynamicS3Client customS3Client,
    IOptions<S3ConfigurationOptions> options,
    IConfiguration configuration,
    ILogger<ReportsUploadService> logger)
    : IReportsUploadService
{
    private readonly S3ConfigurationOptions _s3ConfigurationOptions = options.Value;

    public async Task UploadLoanTapeToS3(byte[] fileBytes, string fileName, CancellationToken ctx)
    {
        logger.LogInformation("Started uploading loan tape report {FileName} to BlueTape S3 bucket", fileName);
        var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);

        var isProduction = IsProductionEnvironment(env);
        await UploadToBlueTapeBucket(fileBytes, fileName, env, ctx);

        logger.LogInformation("Uploaded loan tape report to {FileName} BlueTape S3 bucket", fileName);

        if (isProduction)
        {
            logger.LogInformation("Uploading loan tape report {FileName} to Arcadia S3 bucket", fileName);
            await UploadToArcadia(fileBytes, fileName, ctx);
            logger.LogInformation("Uploaded loan tape report {FileName} to Arcadia S3 bucket", fileName);
        }
    }

    private Task UploadToBlueTapeBucket(byte[] content, string fileName, string? env, CancellationToken ctx)
    {
        var bucketName = GetBlueTapeBucketName(env, fileName);
        var region = _s3ConfigurationOptions.BlueTapeDefaultAwsRegion;
        var s3ConnectionOptions = new S3ConnectionOptions
        {
            RegionEndpoint = RegionEndpoint.GetBySystemName(region)
        };

        return customS3Client.SaveBinaryAsync(content, fileName, bucketName, s3ConnectionOptions, ctx);
    }

    private string GetBlueTapeBucketName(string? env, string fileName)
    {
        var isProd = IsProductionEnvironment(env);
        var isDwhFile = LoanTapeReportConstants.DwhReportsNames.Contains(Path.GetFileNameWithoutExtension(fileName));
        if (isProd && isDwhFile)
            return _s3ConfigurationOptions.DWHAssetsS3BucketName ??
                   throw new VariableNullException(nameof(_s3ConfigurationOptions.DWHAssetsS3BucketName));

        var loanTapeBucketName = _s3ConfigurationOptions.S3BucketName ??
                                 throw new VariableNullException(nameof(_s3ConfigurationOptions.S3BucketName));

        return $"{env}.{loanTapeBucketName}";
    }

    public async Task<bool> IsFileExistInS3Bucket(string fileName, CancellationToken ctx)
    {
        try
        {
            var env = Environment.GetEnvironmentVariable(EnvironmentConstants.AspnetcoreEnvironment);
            var bucketName = GetBlueTapeBucketName(env, fileName);
            var region = _s3ConfigurationOptions.BlueTapeDefaultAwsRegion;
            var s3ConnectionOptions = new S3ConnectionOptions
            {
                RegionEndpoint = RegionEndpoint.GetBySystemName(region)
            };

            return await customS3Client.IsFileExistInS3BucketAsync(bucketName, fileName, s3ConnectionOptions, ctx);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking if file {FileName} exists in S3", fileName);
            return false;
        }
    }

    private static bool IsProductionEnvironment(string? env)
    {
        return !string.IsNullOrEmpty(env) &&
               env.Equals(EnvironmentConstants.Production, StringComparison.InvariantCultureIgnoreCase);
    }

    private Task UploadToArcadia(byte[] content, string fileName, CancellationToken ctx)
    {
        var bucketName = _s3ConfigurationOptions.ArcadiaS3BucketName ??
                         throw new VariableNullException(nameof(_s3ConfigurationOptions.ArcadiaS3BucketName));
        var region = _s3ConfigurationOptions.ArcadiaAwsRegion ??
                     throw new VariableNullException(nameof(_s3ConfigurationOptions.ArcadiaAwsRegion));

        var accessKey = configuration[_s3ConfigurationOptions.ArcadiaS3BucketAccessKey
                                      ?? throw new VariableNullException(nameof(_s3ConfigurationOptions
                                          .ArcadiaS3BucketAccessKey))];
        var secretKey = configuration[_s3ConfigurationOptions.ArcadiaS3BucketSecretKey
                                      ?? throw new VariableNullException(nameof(_s3ConfigurationOptions
                                          .ArcadiaS3BucketSecretKey))];

        var s3ConnectionOptions = new S3ConnectionOptions()
        {
            RegionEndpoint = RegionEndpoint.GetBySystemName(region),
            AccessKey = accessKey,
            SecretKey = secretKey
        };
        return customS3Client.SaveBinaryAsync(content, fileName, bucketName, s3ConnectionOptions, ctx);
    }
}