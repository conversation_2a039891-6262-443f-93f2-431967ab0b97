{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\obj\\Debug\\net8.0\\WorkerExtensions\\WorkerExtensions.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\obj\\Debug\\net8.0\\WorkerExtensions\\WorkerExtensions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\obj\\Debug\\net8.0\\WorkerExtensions\\WorkerExtensions.csproj", "projectName": "Microsoft.Azure.Functions.Worker.Extensions", "projectPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\obj\\Debug\\net8.0\\WorkerExtensions\\WorkerExtensions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\Functions\\AutoPay\\BlueTape.Functions.LMS.GetDue\\obj\\Debug\\net8.0\\WorkerExtensions\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\source\\repos\\BlueTape\\loan-management-service\\BlueTape.Services.LMS\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.pkg.github.com/bluetape-org/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.NET.Sdk.Functions": {"target": "Package", "version": "[4.3.0, )"}, "Microsoft.NETCore.Targets": {"suppressParent": "All", "target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}