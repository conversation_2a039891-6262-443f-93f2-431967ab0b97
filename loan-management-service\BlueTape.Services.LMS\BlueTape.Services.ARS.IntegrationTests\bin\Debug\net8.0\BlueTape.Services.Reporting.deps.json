{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"BlueTape.Services.Reporting/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "BlueTape.Common.FileService": "1.0.6", "BlueTape.CompanyClient": "1.0.53", "BlueTape.CompanyService": "1.3.4", "BlueTape.DataAccess.Mongo": "1.0.0", "BlueTape.OBS.Client": "1.0.0", "BlueTape.Services.DataAccess.External": "1.0.0", "BlueTape.Services.LMS.DataAccess.Company": "1.0.0", "BlueTape.Services.Reporting.DataAccess": "1.0.0", "BlueTape.Services.Reporting.Domain": "1.0.0", "BlueTape.AWSS3": "1.1.5", "BlueTape.AzureKeyVault": "1.0.3", "BlueTape.EmailSender": "3.0.7"}, "runtime": {"BlueTape.Services.Reporting.dll": {}}}, "Audit.EntityFramework.Core/27.1.1": {"dependencies": {"Audit.NET": "27.1.1", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "runtime": {"lib/net8.0/Audit.EntityFramework.Core.dll": {"assemblyVersion": "27.1.1.0", "fileVersion": "27.1.1.0"}}}, "Audit.NET/27.1.1": {"runtime": {"lib/net8.0/Audit.NET.dll": {"assemblyVersion": "27.1.1.0", "fileVersion": "27.1.1.0"}}}, "AutoFilter.Sql/2.0.0": {"runtime": {"lib/netstandard2.0/AutoFilter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.1"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "1*******", "fileVersion": "12.0.1.0"}}}, "AWSSDK.Core/3.7.302.6": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.302.6"}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.2": {"dependencies": {"AWSSDK.Core": "3.7.302.6", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.1.0"}}}, "AWSSDK.KeyManagementService/3.7.300.46": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.KeyManagementService.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.300.46"}}}, "AWSSDK.S3/3.7.10": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.S3.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.10.0"}}}, "AWSSDK.SecretsManager/3.7.302.21": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecretsManager.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.302.21"}}}, "AWSSDK.SecretsManager.Caching/1.0.6": {"dependencies": {"AWSSDK.SecretsManager": "3.7.302.21", "Microsoft.Extensions.Caching.Memory": "8.0.0"}, "runtime": {"lib/netstandard2.0/Amazon.SecretsManager.Extensions.Caching.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "AWSSDK.SecurityToken/3.7.300.47": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/net8.0/AWSSDK.SecurityToken.dll": {"assemblyVersion": "*******", "fileVersion": "3.7.300.47"}}}, "AWSSDK.SimpleNotificationService/**********": {"dependencies": {"AWSSDK.Core": "3.7.302.6"}, "runtime": {"lib/netcoreapp3.1/AWSSDK.SimpleNotificationService.dll": {"assemblyVersion": "*******", "fileVersion": "**********"}}}, "Azure.Core/1.36.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.36.0.0", "fileVersion": "1.3600.23.56006"}}}, "Azure.Core.Amqp/1.3.0": {"dependencies": {"Microsoft.Azure.Amqp": "2.6.4", "System.Memory": "4.5.5", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Core.Amqp.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.300.23.15207"}}}, "Azure.Identity/1.10.4": {"dependencies": {"Azure.Core": "1.36.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.5", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.10.4.0", "fileVersion": "1.1000.423.56303"}}}, "Azure.Messaging.ServiceBus/7.17.1": {"dependencies": {"Azure.Core": "1.36.0", "Azure.Core.Amqp": "1.3.0", "Microsoft.Azure.Amqp": "2.6.4", "Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Memory.Data": "1.0.2"}, "runtime": {"lib/netstandard2.0/Azure.Messaging.ServiceBus.dll": {"assemblyVersion": "7.17.1.0", "fileVersion": "7.1700.123.60403"}}}, "Azure.Security.KeyVault.Keys/4.5.0": {"dependencies": {"Azure.Core": "1.36.0", "System.Memory": "4.5.5", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Keys.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}}, "Azure.Security.KeyVault.Secrets/4.5.0": {"dependencies": {"Azure.Core": "1.36.0", "System.Memory": "4.5.5", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Security.KeyVault.Secrets.dll": {"assemblyVersion": "4.5.0.0", "fileVersion": "4.500.23.16403"}}}, "BlueTape.AWSS3/1.1.5": {"dependencies": {"AWSSDK.Extensions.NETCore.Setup": "3.7.2", "AWSSDK.S3": "3.7.10", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.AWSS3.dll": {"assemblyVersion": "1.1.2.0", "fileVersion": "1.1.2.0"}}}, "BlueTape.AzureKeyVault/1.0.3": {"dependencies": {"Azure.Identity": "1.10.4", "Azure.Security.KeyVault.Keys": "4.5.0", "Azure.Security.KeyVault.Secrets": "4.5.0", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.AzureKeyVault.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}}, "BlueTape.Common.ExceptionHandling/1.0.8": {"dependencies": {"BlueTape.SNS": "1.0.2"}, "runtime": {"lib/net6.0/BlueTape.Common.ExceptionHandling.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}}, "BlueTape.Common.Extensions/1.1.0": {"runtime": {"lib/net6.0/BlueTape.Common.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Common.FileService/1.0.6": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "ClosedXML": "0.102.3", "CsvHelper": "32.0.3"}, "runtime": {"lib/net6.0/BlueTape.Common.FileService.dll": {"assemblyVersion": "1.0.6.0", "fileVersion": "1.0.6.0"}}}, "BlueTape.Common.Validation/1.0.4": {"dependencies": {"BlueTape.AWSS3": "1.1.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "libphonenumber-csharp": "8.12.45"}, "runtime": {"lib/net6.0/BlueTape.Common.Validation.dll": {"assemblyVersion": "1.0.4.0", "fileVersion": "1.0.4.0"}}}, "BlueTape.CompanyClient/1.0.53": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.CompanyService": "1.3.4", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.CompanyClient.dll": {"assemblyVersion": "1.0.53.0", "fileVersion": "1.0.53.0"}}}, "BlueTape.CompanyService/1.3.4": {"dependencies": {"BlueTape.CompanyService.Common": "1.1.21", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/BlueTape.CompanyService.dll": {"assemblyVersion": "1.3.4.0", "fileVersion": "1.3.4.0"}}}, "BlueTape.CompanyService.Common/1.1.21": {"dependencies": {"BlueTape.LS": "1.1.78", "BlueTape.ServiceBusMessaging": "1.0.9"}, "runtime": {"lib/net6.0/BlueTape.CompanyService.Common.dll": {"assemblyVersion": "1.1.21.0", "fileVersion": "1.1.21.0"}}}, "BlueTape.EmailSender/3.0.7": {"dependencies": {"BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "SendGrid": "9.28.1", "SendGrid.Extensions.DependencyInjection": "1.0.1"}, "runtime": {"lib/net6.0/BlueTape.EmailSender.dll": {"assemblyVersion": "3.0.7.0", "fileVersion": "3.0.7.0"}}}, "BlueTape.InvoiceClient/1.0.21": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.InvoiceService": "1.0.40", "BlueTape.Utilities": "1.4.6", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/BlueTape.InvoiceClient.dll": {"assemblyVersion": "1.0.21.0", "fileVersion": "1.0.21.0"}}}, "BlueTape.InvoiceService/1.0.40": {"dependencies": {"BlueTape.Common.Validation": "1.0.4", "BlueTape.InvoiceService.Common": "1.1.3", "BlueTape.OBS": "1.6.72", "BlueTape.PaymentService": "1.0.15", "FluentValidation": "11.0.2"}, "runtime": {"lib/net6.0/BlueTape.InvoiceService.dll": {"assemblyVersion": "1.0.40.0", "fileVersion": "1.0.40.0"}}}, "BlueTape.InvoiceService.Common/1.1.3": {"runtime": {"lib/net6.0/BlueTape.InvoiceService.Common.dll": {"assemblyVersion": "1.1.3.0", "fileVersion": "1.1.3.0"}}}, "BlueTape.LinqpalClient/1.0.8": {"dependencies": {"BlueTape.Common.ExceptionHandling": "1.0.8", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.Utilities": "1.4.6", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/net6.0/BlueTape.LinqpalClient.dll": {"assemblyVersion": "1.0.8.0", "fileVersion": "1.0.8.0"}}}, "BlueTape.LS/1.1.78": {"dependencies": {"BlueTape.LS.Domain": "1.1.36"}, "runtime": {"lib/net6.0/BlueTape.LS.dll": {"assemblyVersion": "1.1.78.0", "fileVersion": "1.1.78.0"}}}, "BlueTape.LS.Domain/1.1.36": {"dependencies": {"BlueTape.Utilities": "1.4.6"}, "runtime": {"lib/net6.0/BlueTape.LS.Domain.dll": {"assemblyVersion": "1.1.36.0", "fileVersion": "1.1.36.0"}}}, "BlueTape.MongoDB/1.1.32": {"dependencies": {"BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Utilities": "1.4.6", "MongoDB.Driver": "2.25.0"}, "runtime": {"lib/net6.0/BlueTape.MongoDB.dll": {"assemblyVersion": "1.1.6.0", "fileVersion": "1.1.6.0"}}}, "BlueTape.OBS/1.6.72": {"runtime": {"lib/net6.0/BlueTape.OBS.dll": {"assemblyVersion": "1.6.72.0", "fileVersion": "1.6.72.0"}}}, "BlueTape.PaymentService/1.0.15": {"dependencies": {"BlueTape.ServiceBusMessaging": "1.0.9", "BlueTape.Utilities": "1.4.6"}, "runtime": {"lib/net6.0/BlueTape.PaymentService.dll": {"assemblyVersion": "1.0.15.0", "fileVersion": "1.0.15.0"}}}, "BlueTape.ServiceBusMessaging/1.0.9": {"dependencies": {"Azure.Messaging.ServiceBus": "7.17.1", "BlueTape.AzureKeyVault": "1.0.3"}, "runtime": {"lib/net6.0/BlueTape.ServiceBusMessaging.dll": {"assemblyVersion": "1.0.9.0", "fileVersion": "1.0.9.0"}}}, "BlueTape.SNS/1.0.2": {"dependencies": {"AWSSDK.SimpleNotificationService": "**********", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Serilog.AspNetCore": "6.0.1"}, "runtime": {"lib/net6.0/BlueTape.SNS.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "BlueTape.Utilities/1.4.6": {"dependencies": {"AWSSDK.KeyManagementService": "3.7.300.46", "AWSSDK.SecretsManager": "3.7.302.21", "AWSSDK.SecretsManager.Caching": "1.0.6", "AWSSDK.SecurityToken": "3.7.300.47", "BlueTape.Common.Extensions": "1.1.0", "BlueTape.OBS": "1.6.72", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Http.Polly": "6.0.9", "Microsoft.Extensions.Options": "8.0.1", "MongoDB.Bson": "2.25.0", "Polly": "7.2.3", "Serilog": "2.12.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Enrichers.GlobalLogContext": "2.1.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Logz.Io": "7.1.0"}, "runtime": {"lib/net6.0/BlueTape.Utilities.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ClosedXML/0.102.3": {"dependencies": {"DocumentFormat.OpenXml": "2.16.0", "ExcelNumberFormat": "1.1.0", "SixLabors.Fonts": "1.0.0", "System.IO.Packaging": "6.0.0", "System.Net.Http": "4.3.4", "System.Text.RegularExpressions": "4.3.1", "XLParser": "1.5.2"}, "runtime": {"lib/netstandard2.1/ClosedXML.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "CsvHelper/32.0.3": {"runtime": {"lib/net8.0/CsvHelper.dll": {"assemblyVersion": "3*******", "fileVersion": "*********"}}}, "DateOnlyTimeOnly.AspNet/2.1.1": {"runtime": {"lib/net7.0/DateOnlyTimeOnly.AspNet.dll": {"assemblyVersion": "2.1.1.0", "fileVersion": "2.1.1.0"}}}, "DnsClient/1.6.1": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net5.0/DnsClient.dll": {"assemblyVersion": "1.6.1.0", "fileVersion": "1.6.1.0"}}}, "DocumentFormat.OpenXml/2.16.0": {"dependencies": {"System.IO.Packaging": "6.0.0"}, "runtime": {"lib/netstandard2.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "2.16.0.0", "fileVersion": "2.16.0.0"}}}, "Elastic.CommonSchema/1.5.3": {"dependencies": {"System.Text.Json": "8.0.0"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.5.3.0"}}}, "Elastic.CommonSchema.Serilog/1.5.3": {"dependencies": {"Elastic.CommonSchema": "1.5.3", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Elastic.CommonSchema.Serilog.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.5.3.0"}}}, "ExcelNumberFormat/1.1.0": {"runtime": {"lib/netstandard2.0/ExcelNumberFormat.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FluentValidation/11.0.2": {"runtime": {"lib/net6.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "1*******"}}}, "Irony.NetCore/1.0.11": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Reflection": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Numerics": "4.3.0"}, "runtime": {"lib/netstandard1.6/Irony.dll": {"assemblyVersion": "1.0.11.0", "fileVersion": "1.0.11.0"}}}, "libphonenumber-csharp/8.12.45": {"dependencies": {"System.Collections.Immutable": "1.7.1"}, "runtime": {"lib/netstandard2.0/PhoneNumbers.dll": {"assemblyVersion": "8.12.45.0", "fileVersion": "8.12.45.0"}}}, "Macross.Json.Extensions/3.0.0": {"runtime": {"lib/net6.0/Macross.Json.Extensions.dll": {"assemblyVersion": "3.0.0.22164", "fileVersion": "*******"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "8.0.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Azure.Amqp/2.6.4": {"runtime": {"lib/netstandard2.0/Microsoft.Azure.Amqp.dll": {"assemblyVersion": "2.4.0.0", "fileVersion": "2.6.4.0"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.InMemory/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/3.0.0": {"dependencies": {"System.Text.Json": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.19.46305"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Http.Polly/6.0.9": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.922.41926"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.123.58001"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Identity.Client/4.56.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.22.0"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.56.0.0", "fileVersion": "4.56.0.0"}}}, "Microsoft.IdentityModel.Abstractions/6.22.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.22.0.30727"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "MongoDB.Bson/2.25.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.1/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.25.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Driver.Core": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver.Core/2.25.0": {"dependencies": {"AWSSDK.SecurityToken": "3.7.300.47", "DnsClient": "1.6.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "MongoDB.Bson": "2.25.0", "MongoDB.Libmongocrypt": "1.8.2", "SharpCompress": "0.30.1", "Snappier": "1.0.0", "System.Buffers": "4.5.1", "ZstdSharp.Port": "0.7.3"}, "runtime": {"lib/netstandard2.1/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.8.2": {"runtime": {"lib/netstandard2.1/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "1.8.2.0", "fileVersion": "1.8.2.0"}}, "runtimeTargets": {"runtimes/linux/native/libmongocrypt.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmongocrypt.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win/native/mongocrypt.dll": {"rid": "win", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "Npgsql/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.0.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Npgsql": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.0.0"}}}, "Polly/7.2.3": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.2.3.0"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.3"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SendGrid/9.28.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "starkbank-ecdsa": "1.3.3"}, "runtime": {"lib/netstandard2.0/SendGrid.dll": {"assemblyVersion": "9.28.1.0", "fileVersion": "9.28.1.0"}}}, "SendGrid.Extensions.DependencyInjection/1.0.1": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "SendGrid": "9.28.1"}, "runtime": {"lib/netstandard2.0/SendGrid.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/2.12.0": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.1.0", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Enrichers.GlobalLogContext.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Serilog": "2.12.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Settings.Configuration/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "3.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.0", "Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/4.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Serilog.Sinks.Http/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Http.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.0.0"}}}, "Serilog.Sinks.Logz.Io/7.1.0": {"dependencies": {"Elastic.CommonSchema.Serilog": "1.5.3", "Newtonsoft.Json": "13.0.3", "Serilog.Sinks.Http": "8.0.0", "Serilog.Sinks.PeriodicBatching": "3.1.0"}, "runtime": {"lib/net6.0/Serilog.Sinks.Logz.Io.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"dependencies": {"Serilog": "2.12.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.30.1": {"runtime": {"lib/net5.0/SharpCompress.dll": {"assemblyVersion": "0.30.1.0", "fileVersion": "0.30.1.0"}}}, "SixLabors.Fonts/1.0.0": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Snappier/1.0.0": {"runtime": {"lib/net5.0/Snappier.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "starkbank-ecdsa/1.3.3": {"runtime": {"lib/netstandard2.1/StarkbankEcdsa.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.5.1": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/1.7.1": {}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.1", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.IO.Packaging/6.0.0": {"runtime": {"lib/net6.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "TinyHelpers/3.0.2": {"dependencies": {"System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/TinyHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.5254"}}}, "TinyHelpers.EntityFrameworkCore/3.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "TinyHelpers": "3.0.2"}, "runtime": {"lib/net8.0/TinyHelpers.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "3.0.2.17739"}}}, "XLParser/1.5.2": {"dependencies": {"Irony.NetCore": "1.0.11", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.6/XLParser.dll": {"assemblyVersion": "1.5.2.0", "fileVersion": "1.5.2.0"}}}, "ZstdSharp.Port/0.7.3": {"runtime": {"lib/net7.0/ZstdSharp.dll": {"assemblyVersion": "0.7.3.0", "fileVersion": "0.7.3.0"}}}, "BlueTape.DataAccess.Mongo/1.0.0": {"dependencies": {"BlueTape.MongoDB": "1.1.32", "BlueTape.Services.LMS.Infrastructure": "1.0.0"}, "runtime": {"BlueTape.DataAccess.Mongo.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.OBS.Client/1.0.0": {"dependencies": {"BlueTape.OBS": "1.6.72", "BlueTape.Utilities": "1.4.6", "DateOnlyTimeOnly.AspNet": "2.1.1", "Macross.Json.Extensions": "3.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"BlueTape.OBS.Client.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.DataAccess.External/1.0.0": {"dependencies": {"BlueTape.CompanyClient": "1.0.53", "BlueTape.CompanyService": "1.3.4", "BlueTape.CompanyService.Common": "1.1.21", "BlueTape.InvoiceClient": "1.0.21", "BlueTape.InvoiceService": "1.0.40", "BlueTape.InvoiceService.Common": "1.1.3", "BlueTape.LinqpalClient": "1.0.8", "BlueTape.OBS": "1.6.72", "BlueTape.OBS.Client": "1.0.0", "BlueTape.PaymentService": "1.0.15", "BlueTape.ServiceBusMessaging": "1.0.9"}, "runtime": {"BlueTape.Services.DataAccess.External.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.LMS.DataAccess/1.0.0": {"dependencies": {"AutoFilter.Sql": "2.0.0", "BlueTape.AzureKeyVault": "1.0.3", "BlueTape.Services.LMS.Domain": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0", "BlueTape.Services.Reporting.Domain": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.InMemory": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.0", "TinyHelpers.EntityFrameworkCore": "3.0.2"}, "runtime": {"BlueTape.Services.LMS.DataAccess.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.LMS.DataAccess.Company/1.0.0": {"dependencies": {"BlueTape.CompanyClient": "1.0.53", "BlueTape.CompanyService": "1.3.4", "BlueTape.CompanyService.Common": "1.1.21", "BlueTape.DataAccess.Mongo": "1.0.0", "BlueTape.Services.LMS.Infrastructure": "1.0.0"}, "runtime": {"BlueTape.Services.LMS.DataAccess.Company.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.LMS.Domain/1.0.0": {"dependencies": {"Audit.EntityFramework.Core": "27.1.1", "AutoFilter.Sql": "2.0.0", "BlueTape.LS.Domain": "1.1.36", "BlueTape.OBS": "1.6.72", "BlueTape.Utilities": "1.4.6", "BlueTape.LS": "1.1.78"}, "runtime": {"BlueTape.Services.LMS.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.LMS.Infrastructure/1.0.0": {"runtime": {"BlueTape.Services.LMS.Infrastructure.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.Reporting.DataAccess/1.0.0": {"dependencies": {"BlueTape.Services.LMS.DataAccess": "1.0.0", "BlueTape.Services.Reporting.Domain": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"BlueTape.Services.Reporting.DataAccess.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BlueTape.Services.Reporting.Domain/1.0.0": {"dependencies": {"BlueTape.Services.LMS.Domain": "1.0.0"}, "runtime": {"BlueTape.Services.Reporting.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"BlueTape.Services.Reporting/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Audit.EntityFramework.Core/27.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-h2qzCHOoKesGiaKYFIkXKDdXluNXeBJmu5QyphEzJyxYSHqX3L4GPgqhs6Aa/f0JFuTIUVup+h8IoHryjoMMag==", "path": "audit.entityframework.core/27.1.1", "hashPath": "audit.entityframework.core.27.1.1.nupkg.sha512"}, "Audit.NET/27.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-M8CVk8EGtgDJoCU+TPivuGffrpDYa9M78Wahqm9cXMVsdUItBVAte9aVi9NWVF0DmDZx6iw4ikp2IXfEWQ0GIw==", "path": "audit.net/27.1.1", "hashPath": "audit.net.27.1.1.nupkg.sha512"}, "AutoFilter.Sql/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7J2H9GPLQvU21rxg2hpVfgq5KfuPSFk7yLcLZQsxtQJvst7W5epDxhkQ7UGnfTd+cipGKpDj3hUTuzkg+0xA8A==", "path": "autofilter.sql/2.0.0", "hashPath": "autofilter.sql.2.0.0.nupkg.sha512"}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "AWSSDK.Core/3.7.302.6": {"type": "package", "serviceable": true, "sha512": "sha512-eOPMHT3YSLQg5wXm+UL8Qoka17byK5l/4qxfuE/S32IoQ34gkTDQ1yI9pQPk8LEpeV/W0BE2np+XuhdDQ3wE8g==", "path": "awssdk.core/3.7.302.6", "hashPath": "awssdk.core.3.7.302.6.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/3.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iFjbEnVB0f6Hr8L3EfdelHG7zxVQrOmeP9UIrX3IODR1eTsrqVrmq0mazdIr0GZK1YG2/DZiVt6tNyV1bayndw==", "path": "awssdk.extensions.netcore.setup/3.7.2", "hashPath": "awssdk.extensions.netcore.setup.3.7.2.nupkg.sha512"}, "AWSSDK.KeyManagementService/3.7.300.46": {"type": "package", "serviceable": true, "sha512": "sha512-6J3FlWpFvEhYinkmabY8i9w66lgvmjlZG5oRHazqyiyatjMOYsA76Ynj+T6gtHC5iB1BrBtEFOPO1rq36sILfA==", "path": "awssdk.keymanagementservice/3.7.300.46", "hashPath": "awssdk.keymanagementservice.3.7.300.46.nupkg.sha512"}, "AWSSDK.S3/3.7.10": {"type": "package", "serviceable": true, "sha512": "sha512-vsNA29rhuwfILkHhPvpm2DHQaqn2TndpwStmhQMnqTRGB+mL1IG+xAdicEIrWrdOsAP4SD9JONYe9CJMcXcnUg==", "path": "awssdk.s3/3.7.10", "hashPath": "awssdk.s3.3.7.10.nupkg.sha512"}, "AWSSDK.SecretsManager/3.7.302.21": {"type": "package", "serviceable": true, "sha512": "sha512-Ns8mQtXUWAhp32xJTeftmAklzQeu0Tful0BTZjQg5EeyYE+qUDQgCrY31UBUr1PVw2sInmT5BBzoyhic9OjAwA==", "path": "awssdk.secretsmanager/3.7.302.21", "hashPath": "awssdk.secretsmanager.3.7.302.21.nupkg.sha512"}, "AWSSDK.SecretsManager.Caching/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-N982+rohMJ/w8ywyN6hgnSgw2cpqj4MJDizz+b93gudQSEzR3lCTzHGN3AQl+ngMH4yTG+DfKgmL7QajvvYyKQ==", "path": "awssdk.secretsmanager.caching/1.0.6", "hashPath": "awssdk.secretsmanager.caching.1.0.6.nupkg.sha512"}, "AWSSDK.SecurityToken/3.7.300.47": {"type": "package", "serviceable": true, "sha512": "sha512-wBcBC0axwf+qQTVMPUblDxIpMtbp04z9ElYeW6BSjeAO7spvJXhEz7yhikdTn8YaeWW+K6U9h6AIDQpJlhH2vQ==", "path": "awssdk.securitytoken/3.7.300.47", "hashPath": "awssdk.securitytoken.3.7.300.47.nupkg.sha512"}, "AWSSDK.SimpleNotificationService/**********": {"type": "package", "serviceable": true, "sha512": "sha512-RqBqzwh9dGCxmfaUGvdHyYqBzd4RyCGUdd8w8zo4nxCH/3acKDbQriiFRgYvSOkOunIKXNMR45aBbQXeReuVMw==", "path": "awssdk.simplenotificationservice/**********", "hashPath": "awssdk.simplenotificationservice.**********.nupkg.sha512"}, "Azure.Core/1.36.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwqFZdHS4dzPlI7FFRkPx9ctA+aGGeRev3gnzG8lntWvKMmBhAmulABi1O9CEvS3/jzYt7yA+0pqVdxkpAd7dQ==", "path": "azure.core/1.36.0", "hashPath": "azure.core.1.36.0.nupkg.sha512"}, "Azure.Core.Amqp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6GG4gyFkAuHtpBVkvj0wE5+lCM+ttsZlIWAipBkI+jlCUlTgrTiNUROBFnb8xuKoymVDw9Tf1W8RoKqgbd71lg==", "path": "azure.core.amqp/1.3.0", "hashPath": "azure.core.amqp.1.3.0.nupkg.sha512"}, "Azure.Identity/1.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-hSvisZy9sld0Gik1X94od3+rRXCx+AKgi+iLH6fFdlnRZRePn7RtrqUGSsORiH2h8H2sc4NLTrnuUte1WL+QuQ==", "path": "azure.identity/1.10.4", "hashPath": "azure.identity.1.10.4.nupkg.sha512"}, "Azure.Messaging.ServiceBus/7.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-RvpLKmp2ur7hfm7NqiKPY2wIU7O4+yajYm3w7etnDsNj6sMlLCyNyCNMgVGeudQ4nOrk0YtoHJ1SbX2nJpCsUw==", "path": "azure.messaging.servicebus/7.17.1", "hashPath": "azure.messaging.servicebus.7.17.1.nupkg.sha512"}, "Azure.Security.KeyVault.Keys/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-HnW9kjhRzQkfJE4ISl63cWVa6qLe3FM1MxoxNvNFtDUeT5iMBEg0YgGbcx2YgEiYaazIvSgZyjBr4L3Ur3+m7g==", "path": "azure.security.keyvault.keys/4.5.0", "hashPath": "azure.security.keyvault.keys.4.5.0.nupkg.sha512"}, "Azure.Security.KeyVault.Secrets/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztr26Ai4K7AZGuw68/ffLDn+2G3WL0myjC7nY1RYkxPMnsplTPEH+Ke4RGxvSkg4kC7bJ9NwdlqpEwfDX0qhdw==", "path": "azure.security.keyvault.secrets/4.5.0", "hashPath": "azure.security.keyvault.secrets.4.5.0.nupkg.sha512"}, "BlueTape.AWSS3/1.1.5": {"type": "package", "serviceable": true, "sha512": "sha512-R+A6Zy9RuUnYkuIZTcT1v1WZEWpK4pDINAPTHWg3hYVlfWg7WA24ZgCD/0VO6sUyFobkrj078Blfl9ED/UMy+Q==", "path": "bluetape.awss3/1.1.5", "hashPath": "bluetape.awss3.1.1.5.nupkg.sha512"}, "BlueTape.AzureKeyVault/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-On4RZI41X71GSmrYnv3p1eN6ullTWF9L8SsZ2NC/tQsQ/Upe0X1kcJE7rgvrT6G1RToKyY2n+OAGwfYZH8uOlQ==", "path": "bluetape.azurekeyvault/1.0.3", "hashPath": "bluetape.azurekeyvault.1.0.3.nupkg.sha512"}, "BlueTape.Common.ExceptionHandling/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-/xF77tfLqtZck0m82ynWFshDGYCWEAlqS3iUiSYrM37GF3J9l/kw/AA6T2LJlmePnyGGVwufPIJQpFcd/2dhbQ==", "path": "bluetape.common.exceptionhandling/1.0.8", "hashPath": "bluetape.common.exceptionhandling.1.0.8.nupkg.sha512"}, "BlueTape.Common.Extensions/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-P4mQRSipiN2qp+5ETfnBdZz564U1AWder867Zj/4SV+mZTS+SxqML0H+sW2W7PADp9iUgdngoNzqYsj+UxGheA==", "path": "bluetape.common.extensions/1.1.0", "hashPath": "bluetape.common.extensions.1.1.0.nupkg.sha512"}, "BlueTape.Common.FileService/1.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-8N5R1YPx1oe6w9iU1je5NKg085RVumKDtDzvjLRqYqCbZSr+C47dKe2XjUrLlttrqebqagFdKFs0eLMu94n3HQ==", "path": "bluetape.common.fileservice/1.0.6", "hashPath": "bluetape.common.fileservice.1.0.6.nupkg.sha512"}, "BlueTape.Common.Validation/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-IA6/J881MMavf8qQ28vuZTvcaCU0gOAhZNXZxvtvs9BgXUHlWUx0LWfD9pwKTPNTiqqUub4XFeC34pXgO4bA0w==", "path": "bluetape.common.validation/1.0.4", "hashPath": "bluetape.common.validation.1.0.4.nupkg.sha512"}, "BlueTape.CompanyClient/1.0.53": {"type": "package", "serviceable": true, "sha512": "sha512-Jsl9IhdSskCZU986n7LxmwdFHMPMPQwqWoGOCK5Ich8X5xcEHOFdr3ish8MBZslciFzXuVvDueHJVFKvFWeZzQ==", "path": "bluetape.companyclient/1.0.53", "hashPath": "bluetape.companyclient.1.0.53.nupkg.sha512"}, "BlueTape.CompanyService/1.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-45U106omsh6weMvGaBSf3fY+yswpW/tsLXvHBJKZgjh40tYYe6j7gMrCXC8mqvCZ+wnNp1NryP3oPsR9P8PP1w==", "path": "bluetape.companyservice/1.3.4", "hashPath": "bluetape.companyservice.1.3.4.nupkg.sha512"}, "BlueTape.CompanyService.Common/1.1.21": {"type": "package", "serviceable": true, "sha512": "sha512-1znqFDa5Gyglpcp3Eq3vh6r88ahgWhhMg6Duja1guXhjdWiRDDbIN8wPYoN5+i/IeW/aqLlyTr7pxapJ28qxwg==", "path": "bluetape.companyservice.common/1.1.21", "hashPath": "bluetape.companyservice.common.1.1.21.nupkg.sha512"}, "BlueTape.EmailSender/3.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-/qaKlPPGOu/EvdKdGGKvq9xKpRq/37hrqzX/TgtQhUkWmEydufTdilB93R7lo7owIpkNkadbsbgPjUHlA1HzXA==", "path": "bluetape.emailsender/3.0.7", "hashPath": "bluetape.emailsender.3.0.7.nupkg.sha512"}, "BlueTape.InvoiceClient/1.0.21": {"type": "package", "serviceable": true, "sha512": "sha512-Pp/hOsOzFTmHpzjQgYndv8AyxX8cmT8/XVN5cHkHEsC3k8y/Vs8+ATspCCDoz3oqBRBnZQCVmmkWo2sVrKMbVQ==", "path": "bluetape.invoiceclient/1.0.21", "hashPath": "bluetape.invoiceclient.1.0.21.nupkg.sha512"}, "BlueTape.InvoiceService/1.0.40": {"type": "package", "serviceable": true, "sha512": "sha512-ot/eDvPfaZ0J935VDR0r6okalj1/fRc+L5+rb5zXE9ueyQVmGHHrDtEc288MUQ3pm0IC8XMxHOzDASMM3zGREw==", "path": "bluetape.invoiceservice/1.0.40", "hashPath": "bluetape.invoiceservice.1.0.40.nupkg.sha512"}, "BlueTape.InvoiceService.Common/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-bd3nhrpnFHZ4ntWpVcs46EmK1bf/+DSSKy23aoo6ROYfRLD4R9Ky9WSsQKC7kIv3I3gxVd7uOFRQfb71WrVyqQ==", "path": "bluetape.invoiceservice.common/1.1.3", "hashPath": "bluetape.invoiceservice.common.1.1.3.nupkg.sha512"}, "BlueTape.LinqpalClient/1.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-5NZ70zF50ugY62Ar1WWJZn6fTwL3RaoI2fa5bh6w/bzbznb8ve+NpRnQi0KFzOBmzVp3TbFfVG6CngKdg1e+PA==", "path": "bluetape.linqpalclient/1.0.8", "hashPath": "bluetape.linqpalclient.1.0.8.nupkg.sha512"}, "BlueTape.LS/1.1.78": {"type": "package", "serviceable": true, "sha512": "sha512-u8xaG9I3XDkoVkvkzIZJGx1DePnBKowm+FJ9DjOtSqzbYe5FNAJJpW8CIiqjYQxmqhyql+vBIdWQ5mkUijDZnQ==", "path": "bluetape.ls/1.1.78", "hashPath": "bluetape.ls.1.1.78.nupkg.sha512"}, "BlueTape.LS.Domain/1.1.36": {"type": "package", "serviceable": true, "sha512": "sha512-GVGfugV3OtXFHnjm1z429MCf6+Exu/wEXsWtUgqUeSnx2DHh+/MYsEk7Ruaw/daCkdL/cy2rIUerCUkLdgjDuA==", "path": "bluetape.ls.domain/1.1.36", "hashPath": "bluetape.ls.domain.1.1.36.nupkg.sha512"}, "BlueTape.MongoDB/1.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-oeiZKvlFUy87kEQbyOthESrOn4tbsBUBDXIZ6NqpxcSqGIODpKyn6symjuNNoQMIdPbxAOu1yf68yRXmF5M8OQ==", "path": "bluetape.mongodb/1.1.32", "hashPath": "bluetape.mongodb.1.1.32.nupkg.sha512"}, "BlueTape.OBS/1.6.72": {"type": "package", "serviceable": true, "sha512": "sha512-cSgIO2r8PB605JHbx4HLnkQ5YPUqQKlIpPBd1s7dk5v8HMB+GQZKloWycCWzVyV5m6yZJy5A91ecvKqAflK1KQ==", "path": "bluetape.obs/1.6.72", "hashPath": "bluetape.obs.1.6.72.nupkg.sha512"}, "BlueTape.PaymentService/1.0.15": {"type": "package", "serviceable": true, "sha512": "sha512-eomwRAiEvjNOtNzQDlPKP3ILus6f8JpP8Cmg4Xaqf068VQvo22OwXyiEVaJ8rrhWW8cVtoO9w90YIY0qSnIWqg==", "path": "bluetape.paymentservice/1.0.15", "hashPath": "bluetape.paymentservice.1.0.15.nupkg.sha512"}, "BlueTape.ServiceBusMessaging/1.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-+zUFfrqv4Qu4uSGVOZ64tOBygVA/UfDtN2amwgfFF6vyNxnkRhJXeJyepeMOekrdA0wzUbB7U/OrNO7wrvX94g==", "path": "bluetape.servicebusmessaging/1.0.9", "hashPath": "bluetape.servicebusmessaging.1.0.9.nupkg.sha512"}, "BlueTape.SNS/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-yyQoT3RXeiKRFiv/RK6gPVXj4rAR7OXrAUDlIU/f8EztzUgiLsNzazfmzCJRSgLxfmKSJoBkdfqcyBla3Rxkwg==", "path": "bluetape.sns/1.0.2", "hashPath": "bluetape.sns.1.0.2.nupkg.sha512"}, "BlueTape.Utilities/1.4.6": {"type": "package", "serviceable": true, "sha512": "sha512-vD/c4SBIubmwdz8okn2XBUfUMc5kjMOaQJDckOgFSFyhhPjz0JD9sGkHNXq7Py0x8DXujuWSLtVVh77FXS3DMA==", "path": "bluetape.utilities/1.4.6", "hashPath": "bluetape.utilities.1.4.6.nupkg.sha512"}, "ClosedXML/0.102.3": {"type": "package", "serviceable": true, "sha512": "sha512-4PN5JZsloPGfV2iSa2g62CSETQAtHELECwo9rzMvOAibe1fsPv0CNTOXXji+ihXOyEZBZDLhkE7s1MhHJzMxIA==", "path": "closedxml/0.102.3", "hashPath": "closedxml.0.102.3.nupkg.sha512"}, "CsvHelper/32.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-JNDkd4Wc2Tim5IuyL8Ai13RhzRvaHItseEvlJ2YRkx2SMBFrIsW8a5+/o4/O1jsb6mlviihR6Hfyn+nqY6CurA==", "path": "csvhelper/32.0.3", "hashPath": "csvhelper.32.0.3.nupkg.sha512"}, "DateOnlyTimeOnly.AspNet/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-7HlGV6sm0efRMquxnZaoqV3KybUMOs1IaJ4TaX85v7IZF01h1YV6Zscos9g4qQfrGYfvqLjbpe3Q54cmMBeISQ==", "path": "dateonlytimeonly.aspnet/2.1.1", "hashPath": "dateonlytimeonly.aspnet.2.1.1.nupkg.sha512"}, "DnsClient/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-4H/f2uYJOZ+YObZjpY9ABrKZI+JNw3uizp6oMzTXwDw6F+2qIPhpRl/1t68O/6e98+vqNiYGu+lswmwdYUy3gg==", "path": "dnsclient/1.6.1", "hashPath": "dnsclient.1.6.1.nupkg.sha512"}, "DocumentFormat.OpenXml/2.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-RhpnDgyyx1KjZm98T3w3bSXFHG/7ZNUaVmz4NAUA+jmV3PcVNZeW87Y04CpBNLdDHEMSspirfo0B5kLRaoE97w==", "path": "documentformat.openxml/2.16.0", "hashPath": "documentformat.openxml.2.16.0.nupkg.sha512"}, "Elastic.CommonSchema/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-JgwhfThYY/s17asUiBCUVqnZtDdGTWO/2hTPG01QDfw2+T6kfwskrj5eh6XpBZsOh6r9SpBL95vSsU+q44i7Zg==", "path": "elastic.commonschema/1.5.3", "hashPath": "elastic.commonschema.1.5.3.nupkg.sha512"}, "Elastic.CommonSchema.Serilog/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-bp2qHOWmN15fTKUecFMt7oCra68I1cm3yAEmwPcLuz4v2pQ5YxC8nVtyCTSSibquUS/ZPH5JInjlmuywV3UoyQ==", "path": "elastic.commonschema.serilog/1.5.3", "hashPath": "elastic.commonschema.serilog.1.5.3.nupkg.sha512"}, "ExcelNumberFormat/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "path": "excelnumberformat/1.1.0", "hashPath": "excelnumberformat.1.1.0.nupkg.sha512"}, "FluentValidation/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-ik/xogKqBmZWIURMXNEbugJlKka6iV9a4qckmKDp7sHSwIfgkZ/ewzCuAeUTkFdVZujfDW6mCLMjrrR6GG4xLQ==", "path": "fluentvalidation/11.0.2", "hashPath": "fluentvalidation.11.0.2.nupkg.sha512"}, "Irony.NetCore/1.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-/2MCkqdhEjPiSTZKYXg6u9AO9o3m68biqcFHAnM28WdnrcZii2vYbmKPzT5ejByyV72FUlygOtHBMt8SLnUyNw==", "path": "irony.netcore/1.0.11", "hashPath": "irony.netcore.1.0.11.nupkg.sha512"}, "libphonenumber-csharp/8.12.45": {"type": "package", "serviceable": true, "sha512": "sha512-TG+5eh+rJ15LTFFLA/J179O/NwVTpdjreJxWYWSCbT8kFVAhSra9Ap5twi4LFJTMe9zkYxxBHNlnDv8gPfbaHA==", "path": "libphonenumber-csharp/8.12.45", "hashPath": "libphonenumber-csharp.8.12.45.nupkg.sha512"}, "Macross.Json.Extensions/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AkNshs6dopj8FXsmkkJxvLivN2SyDJQDbjcds5lo9+Y6L4zpcoXdmzXQ3VVN+AIWQr0CTD5A7vkuHGAr2aypZg==", "path": "macross.json.extensions/3.0.0", "hashPath": "macross.json.extensions.3.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.Azure.Amqp/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-Xf2mbwTSuUtqRlULKXCEuXPxlBhZzZXWmMxnxF64WJAelo3PA7kIDR4Bv+eOBYxHyr3FJtwG3/7rrhyXIx1Qzg==", "path": "microsoft.azure.amqp/2.6.4", "hashPath": "microsoft.azure.amqp.2.6.4.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/pT9FOO0BxGSRscK/ekEb6TdiP3+nnyhPLElE1yuVG/QaZLaBAuM3RoywBHdIxWoFALaOS7ktXlKzuMX3khJ4A==", "path": "microsoft.entityframeworkcore.inmemory/8.0.0", "hashPath": "microsoft.entityframeworkcore.inmemory.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SsI4RqI8EH00+cYO96tbftlh87sNUv1eeyuBU1XZdQkG0RrHAOjWgl7P0FoLeTSMXJpOnfweeOWj2d1/5H3FxA==", "path": "microsoft.extensions.configuration/2.0.0", "hashPath": "microsoft.extensions.configuration.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IznHHzGUtrdpuQqIUdmzF6TYPcsYHONhHh3o9dGp39sX/9Zfmt476UnhvU0UhXgJnXXAikt/MpN6AuSLCCMdEQ==", "path": "microsoft.extensions.configuration.binder/2.0.0", "hashPath": "microsoft.extensions.configuration.binder.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Iaectmzg9Dc4ZbKX/FurrRjgO/I8rTumL5UU+Uube6vZuGetcnXoIgTA94RthFWePhdMVm8MMhVFJZdbzMsdyQ==", "path": "microsoft.extensions.dependencymodel/3.0.0", "hashPath": "microsoft.extensions.dependencymodel.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-+tQeERLaSPA+G//SlIZ5pyv/jAmkn1xnMMOvFu3Bag3EJxwV4D9iEkHD2TaNiJOoFZ/VROUB76/H7n/75e9Dow==", "path": "microsoft.extensions.http.polly/6.0.9", "hashPath": "microsoft.extensions.http.polly.6.0.9.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wmpp+BSU3oGifaev6Z9rrlwHoITLFfpVOSbgBrOXjkbJSCXnZVCsoRGE5c3fJFI4VlNgnNkNlI9y+5jC4fmOEA==", "path": "microsoft.extensions.options/8.0.1", "hashPath": "microsoft.extensions.options.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/lGICwO27fCkQRK3tZseVzFjZaxfGmui990E67sB4MuiPzdJHnJDS/BeYWrHShSSBgCl4KyKRx4ux686fftPg==", "path": "microsoft.extensions.options.configurationextensions/2.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-rr4zbidvHy9r4NvOAs5hdd964Ao2A0pAeFBJKR95u1CJAVzbd1p6tPTXUZ+5ld0cfThiVSGvz6UHwY6JjraTpA==", "path": "microsoft.identity.client/4.56.0", "hashPath": "microsoft.identity.client.4.56.0.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.56.0": {"type": "package", "serviceable": true, "sha512": "sha512-H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "path": "microsoft.identity.client.extensions.msal/4.56.0", "hashPath": "microsoft.identity.client.extensions.msal.4.56.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-iI+9V+2ciCrbheeLjpmjcqCnhy+r6yCoEcid3nkoFWerHgjVuT6CPM4HODUTtUPe1uwks4wcnAujJ8u+IKogHQ==", "path": "microsoft.identitymodel.abstractions/6.22.0", "hashPath": "microsoft.identitymodel.abstractions.6.22.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "MongoDB.Bson/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-xQx/qtC2nu9oGiyNqAwfiDpUMweLi0nID677cyKykpwmj5AVMrnd//UwmcmuX95178DeY6rf7cjmA613TQXPiA==", "path": "mongodb.bson/2.25.0", "hashPath": "mongodb.bson.2.25.0.nupkg.sha512"}, "MongoDB.Driver/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMqnZTV6MuvoEI4yFtSvKJdAoN6NeyAEvG8aoxnrLIVd7bR84QxLgpsM1nhK17qkOcIx/IrpMIfrvp5iMnYGBg==", "path": "mongodb.driver/2.25.0", "hashPath": "mongodb.driver.2.25.0.nupkg.sha512"}, "MongoDB.Driver.Core/2.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-oN4nLgO5HQEThTg/zqeoHqaO2+q64DBVb4r7BvhaFb0p0TM9jZKnCKvh1EA8d9E9swIz0CgvMrvL1mPyRCZzag==", "path": "mongodb.driver.core/2.25.0", "hashPath": "mongodb.driver.core.2.25.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-z/8JCULSHM1+mzkau0ivIkU9kIn8JEFFSkmYTSaMaWMMHt96JjUtMKuXxeGNGSnHZ5290ZPKIlQfjoWFk2sKog==", "path": "mongodb.libmongocrypt/1.8.2", "hashPath": "mongodb.libmongocrypt.1.8.2.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qiz74U+O7Mv4knrsXgKVYGJjgwoziK+aMFZqz7PtKR3vyGIhZA0tnW6HoUnL3X+YqtmVuhmoKkN8LDWEHMxPbw==", "path": "npgsql/8.0.0", "hashPath": "npgsql.8.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GDXiMS9peEdjSCU/rfgyHruio7q6tYuywGaktqEi6UPQ6ILechp3fVVX+dHXkIXt4nklCBzYVWkzFrSL9ubKUA==", "path": "npgsql.entityframeworkcore.postgresql/8.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.8.0.0.nupkg.sha512"}, "Polly/7.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ==", "path": "polly/7.2.3", "hashPath": "polly.7.2.3.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SendGrid/9.28.1": {"type": "package", "serviceable": true, "sha512": "sha512-LyIkgjd+svXuQxpqe5pvyOccyUdKcDqwnBNDPjyCngkKeVpXAOTAr3U1DBLWqHEbFHvu2UBFki3SJzDwxvJdfA==", "path": "sendgrid/9.28.1", "hashPath": "sendgrid.9.28.1.nupkg.sha512"}, "SendGrid.Extensions.DependencyInjection/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-M3dHAkRIIDWvGNro5S25xjQ+nvUTomZ5er12TL0Re+G2UwIntMvO2OthECb3SV28AvOtDd4yZERjdHTrJ+gD1w==", "path": "sendgrid.extensions.dependencyinjection/1.0.1", "hashPath": "sendgrid.extensions.dependencyinjection.1.0.1.nupkg.sha512"}, "Serilog/2.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-xaiJLIdu6rYMKfQMYUZgTy8YK7SMZjB4Yk50C/u//Z4OsvxkUfSPJy4nknfvwAC34yr13q7kcyh4grbwhSxyZg==", "path": "serilog/2.12.0", "hashPath": "serilog.2.12.0.nupkg.sha512"}, "Serilog.AspNetCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "path": "serilog.aspnetcore/6.0.1", "hashPath": "serilog.aspnetcore.6.0.1.nupkg.sha512"}, "Serilog.Enrichers.GlobalLogContext/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-BtSFymdnkHWYpxJzLAQ7J03pdnkIlQ3kQLswBRz37j2XllEp5cnmCOOTqdw7Hyh8TrHQ1nJSZZ+HTjV4AqTwhA==", "path": "serilog.enrichers.globallogcontext/2.1.0", "hashPath": "serilog.enrichers.globallogcontext.2.1.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "path": "serilog.settings.configuration/3.3.0", "hashPath": "serilog.settings.configuration.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-K6N5q+5fetjnJPvCmkWOpJ/V8IEIoMIB1s86OzBrbxwTyHxdx3pmz4H+8+O/Dc/ftUX12DM1aynx/dDowkwzqg==", "path": "serilog.sinks.console/4.1.0", "hashPath": "serilog.sinks.console.4.1.0.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.Http/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHyl2/93Roymf2eudPl/6Eeu2GQ93Ucy4GM1UPF0jyd7CIW8r+Bk5ohdbjjyjB9TQSpP2ovOuj6ltf9DjoWHtg==", "path": "serilog.sinks.http/8.0.0", "hashPath": "serilog.sinks.http.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Logz.Io/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-uZN5FWMvpk0m/6u7PiKShODbUvfxQZGzK+D91BaxV1ePVJSE0xaqJwCh7rp8/mhPp2C0UPmXOt1hAF306Fzn1Q==", "path": "serilog.sinks.logz.io/7.1.0", "hashPath": "serilog.sinks.logz.io.7.1.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDWR7m3PalVlGEq3rzoktrXikjFMLmpwF0HI4sowo8YDdU+gqPlTHlDQiOGxHfB0sTfjPA9JjA7ctKG9zqjGkw==", "path": "serilog.sinks.periodicbatching/3.1.0", "hashPath": "serilog.sinks.periodicbatching.3.1.0.nupkg.sha512"}, "SharpCompress/0.30.1": {"type": "package", "serviceable": true, "sha512": "sha512-XqD4TpfyYGa7QTPzaGlMVbcecKnXy4YmYLDWrU+JIj7IuRNl7DH2END+Ll7ekWIY8o3dAMWLFDE1xdhfIWD1nw==", "path": "sharpcompress/0.30.1", "hashPath": "sharpcompress.0.30.1.nupkg.sha512"}, "SixLabors.Fonts/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "path": "sixlabors.fonts/1.0.0", "hashPath": "sixlabors.fonts.1.0.0.nupkg.sha512"}, "Snappier/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rFtK2KEI9hIe8gtx3a0YDXdHOpedIf9wYCEYtBEmtlyiWVX3XlCNV03JrmmAi/Cdfn7dxK+k0sjjcLv4fpHnqA==", "path": "snappier/1.0.0", "hashPath": "snappier.1.0.0.nupkg.sha512"}, "starkbank-ecdsa/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-OblOaKb1enXn+dSp7tsx9yjwV+/BEKM9jFhshIkZTwCk7LuTFTp+wSon6rFzuPiIiTGtvVWQNUw2slHjGktJog==", "path": "starkbank-ecdsa/1.3.3", "hashPath": "starkbank-ecdsa.1.3.3.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/1.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-B43<PERSON>sz5EfMwyEbnObwRxW5u85fzJma3lrDeGcSAV1qkhSRTNY5uXAByTn9h9ddNdhM+4/YoLc/CI43umjwIl9Q==", "path": "system.collections.immutable/1.7.1", "hashPath": "system.collections.immutable.1.7.1.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Packaging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C7OkTRIjqIjAKu6ef/fuj8ynCZTPcTYZnvHaq48bniACgXXJogmEoIc56YCDNTc14xhsbLmgpS3KP+evbsUa2g==", "path": "system.io.packaging/6.0.0", "hashPath": "system.io.packaging.6.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "TinyHelpers/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Fg5gvCWBqB40ba33PMK831OQAdpN7Ny2+QWh7lxdfk47vbUtxncKJiC6fhRz/NxmsNp8gWHdRh187TscXAIhPA==", "path": "tinyhelpers/3.0.2", "hashPath": "tinyhelpers.3.0.2.nupkg.sha512"}, "TinyHelpers.EntityFrameworkCore/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JioI+R+zijUZxy6Utbo5/KVM9E88BjSim8PhsbMNAlQHNPigxhGqfGGR23uLzvGPS67lyDTesBXaUimqN7+v8A==", "path": "tinyhelpers.entityframeworkcore/3.0.2", "hashPath": "tinyhelpers.entityframeworkcore.3.0.2.nupkg.sha512"}, "XLParser/1.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-8tnUCmMbn6c/HGuHFyq1sulHym8YReTKDNXFFQk0U8nldNx6RGrwRvLh0+aQjEc7YoC3Q1ztH1npLt7dClKqjw==", "path": "xlparser/1.5.2", "hashPath": "xlparser.1.5.2.nupkg.sha512"}, "ZstdSharp.Port/0.7.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9Ix4l4cl58Kzz1rJzj5hoVTjmbx1qGMwzAcbv1j/d3NzrFaESIurQyg+ow4mivCgkE3S413y+U9k4WdnEIkRA==", "path": "zstdsharp.port/0.7.3", "hashPath": "zstdsharp.port.0.7.3.nupkg.sha512"}, "BlueTape.DataAccess.Mongo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.OBS.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.DataAccess.External/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.DataAccess/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.DataAccess.Company/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.LMS.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.Reporting.DataAccess/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BlueTape.Services.Reporting.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}