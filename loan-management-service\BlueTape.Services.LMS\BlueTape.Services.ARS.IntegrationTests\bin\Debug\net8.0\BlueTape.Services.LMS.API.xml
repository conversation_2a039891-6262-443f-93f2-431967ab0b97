<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BlueTape.Services.LMS.API</name>
    </assembly>
    <members>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.CreateManualPayment(BlueTape.LS.DTOs.Payment.ManualPaymentDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Create manual payment with Custom type
             </summary>
             <param name="userId">External user id</param>
             <remarks>
             Sample request:
            
                 POST /Admin/Payments
                 {
                    "LoanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                    "Amount": 100,
                    "Date": "2022-10-20",
                    "Note": "Reason for manual payment"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.CreateRefundPayment(BlueTape.LS.DTOs.Payment.CreateRefundPaymentDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Create refund
             </summary>
             <param name="userId">External user id</param>
             <remarks>
             Sample request:
            
                 POST /Admin/Refunds
                 {
                    "LoanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                    "Amount": 500,
                    "Date": "2022-10-20",
                    "Note": "Reason for refund"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.CreateManualLatePaymentFee(BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees.CreateFeeReceivableDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Create manual late payment fee 
             </summary>
             <param name="userId">External user id</param>
             <remarks>
             Sample request:
            
                 POST /Admin/LatePaymentFee
                 {
                    "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                    "expectedAmount": 100,
                    "expectedDate": "2022-10-20",
                    "note": "Reason for manual late fee creation"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.CreateExtensionFee(BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees.CreateFeeReceivableDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Create extension fee 
             </summary>
             <param name="userId">External user id</param>
             <remarks>
             Sample request:
            
                 POST /Admin/ExtensionFee
                 {
                    "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                    "expectedAmount": 100,
                    "expectedDate": "2022-10-20",
                    "note": "Reason for manual extension fee creation"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.CreatePenaltyInterestFee(BlueTape.LS.DTOs.PenaltyInterest.CreatePenaltyInterestFeeReceivableDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Create penalty interest fee 
             </summary>
             <param name="userId">External user id</param>
             <remarks>
             Sample request:
            
                 POST /Admin/PenaltyInterest
                 {
                    "expectedDate": "2022-10-20",
                    "expectedAmount": 100,
                    "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                    "penaltyStartDate": "2022-10-20",
                    "penaltyEndDate": "2022-10-20",
                    "note": "Reason for manual penalty interest fee creation"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.ChangeExpectedDate(System.Guid,BlueTape.LS.DTOs.LoanReceivable.UpdateLoanReceivableDateDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Change receivable expected date 
             </summary>
             <param name="userId">External user id</param>
             <param name="id">LoanReceivable id</param>
             <remarks>
             Sample request:
            
                 PATCH /Admin/LoanReceivables/{id}
                 {
                    "Date": "2022-10-20",
                    "Note": "Reason for change date"
                 }
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.CancelPayment(System.Guid,BlueTape.LS.DTOs.Payment.UpdateAdminPaymentDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Cancel payment 
             </summary>
             <param name="userId">External user id</param>
             <param name="id">Payment id</param>
             <remarks>
             Sample request:
            
                 PATCH /Admin/Payments/{id}
                 {
                    "Status": "Canceled",
                    "Note": "Reason for change date"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.ChangeLoanStatus(System.Guid,BlueTape.LS.DTOs.Loan.UpdateLoanStatusAdminDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Change loan status to defaulted / recovered / refinanced / canceled
             </summary>
             <param name="userId">External user id</param>
             <param name="id">Loan id</param>
             <remarks>
             Sample request:
            
                 PATCH /Admin/Loans/{id}
                 {
                    "Status": "Defaulted",
                    "Note": "Reason for change status"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.GetLoansByIds(BlueTape.Services.LMS.API.ViewModels.Ids.IdsViewModel,System.Nullable{System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Get Loans by array of Ids
            </summary>
            <remarks>
            Sample request:
            
                POST /Admin/Loans
                {
                   "ids": [
                     {
                       "id": "e7fc1d7a-9f4c-472b-8419-7bb14ca101c6"
                     },
                     {
                       "id": "7f5a25b5-e147-44ba-969e-0d52a6157a79"
                     }
                  ]
                }
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.GetLoansToSync(System.Nullable{System.Boolean},System.Threading.CancellationToken)">
            <summary>
            Sync loans with MongoDb
            </summary>
            <remarks>
            Sample request:
            
                PATCH /Admin/Loans
                    
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.GetByLoanId(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Array LoanReceivables by loan id
            </summary>
            <param name="id">Loan id</param>
            <remarks>
            Sample request:
            
                GET /Admin/Loans/{id}/LoanReceivables
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdateLoanReceivablesByLoanId(System.Guid,BlueTape.LS.DTOs.LoanReceivable.UpdateLoanReceivablesDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Update loan receivables
             </summary>
             <param name="userId">External user id</param>
             <param name="id">Receivable id</param>
             <remarks>
             Sample request:
            
                 PUT /Admin/Loans/{id}/LoanReceivables
                 {
                    "loanReceivables": [
                      {
                        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                        "expectedDate": "2023-06-06",
                        "status": "None",
                        "expectedAmount": 0,
                        "paidAmount": 0
                      }
                    ],
                    "note": "string"
                 }
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.RescheduleReceivables(System.Guid,BlueTape.LS.DTOs.Loan.ReceivablesReschedule.ReceivablesRescheduleDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Reschedule Receivables
             </summary>
             <param name="id">Loan id</param>
             <param name="userId">External user id</param>
             <remarks>
             Sample request:
            
                 POST /Admin/Loans/{id}/LoanReceivables/Reschedule
                 {
                    "loanReceivables": [
                      {
                        "id": "8867a199-02d1-46ca-ada0-e9e286fb6841",
                        "receivableType": "installment",
                        "newExpectedDate": "2023-01-01T00:00:00.000Z",
                        "newScheduleStatus": "postponed",
                        "newExpectedAmount": 1000
                      }
                   ],
                     "note": "Extending duration"
                 }
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdateLatePaymentFeeReceivable(System.Guid,BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees.UpdateReceivableFeeDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Change LatePaymentFee's expected amount or status
             </summary>
             <param name="id">LatePaymentFee id</param>
             /// <remarks>
             Sample request:
            
                 PATCH /Admin/LatePaymentFee/{id}
                 {
                     "status": "Canceled",
                     "note": "Note for changing status"
                 }
             
                 PATCH /Admin/LatePaymentFee/{id}
                 {
                     "expectedAmount": 1000,
                     "note": "Note for changing amount"
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdateExtensionFeeReceivable(System.Guid,BlueTape.LS.DTOs.LoanReceivable.LatePaymentFees.UpdateReceivableFeeDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Change ExtensionFee's expected amount or status
             </summary>
             <param name="id">Extension id</param>
             /// <remarks>
             Sample request:
            
                 PATCH /Admin/ExtensionFee/{id}
                 {
                     "status": "Canceled",
                     "note": "Note for changing status"
                 }
             
                 PATCH /Admin/ExtensionFee/{id}
                 {
                     "expectedAmount": 1000,
                     "note": "Note for changing amount"
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdatePenaltyInterestFeeReceivable(System.Guid,BlueTape.LS.DTOs.LoanReceivable.UpdatePenaltyInterestFeeDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Change PenaltyInterestFee's status or other fields
             </summary>
             <param name="id">PenaltyInterestFee id</param>
             /// <remarks>
             Sample request:
            
                 PATCH /Admin/PenaltyInterest/{id}
                 {
                     "status": "Canceled",
                     "note": "Note for changing status"
                 }
             
                 PATCH /Admin/PenaltyInterest/{id}
                 {
                     "expectedAmount": 0,
                     "expectedDate": "2023-09-15",
                     "penaltyStartDate": "2023-09-15",
                     "penaltyEndDate": "2023-09-15",
                     "note": "Note for changing"
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdateCreditDetails(System.Guid,BlueTape.LS.DTOs.Credit.UpdateCreditDetailsDto,System.String,System.Threading.CancellationToken)">
            <summary>
            Update credit details
            </summary>
            <param name="id">Credit id</param>
            /// <remarks>
            Sample request:
            {
             "creditLimit": 0,
             "purchaseType": "inventory",
            "revenueFallPercentage": 0,
             "notes": "string"
            }
            </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdateLoanPricingPackage(System.String,BlueTape.LS.DTOs.LoanPricingPackages.ShortLoanPricingPackageDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Updates a loan pricing package (admin function) All fields are optional. Which field is sent, that field to update.
             </summary>
             <param name="id">LoanPricingPackage id</param>
              /// <remarks>
             Sample request:
            
             
                 PATCH /Admin/LoanPricingPackages/{id}
             {
                 "id": "bdafd605-fb56-4d32-9c9b-3b58c174dd79",
                 "createdAt": "2024-10-10T10:21:44.277Z",
                 "createdBy": "string",
                 "updatedAt": "2024-10-10T10:21:44.277Z",
                 "updatedBy": "string",
                 "name": "string",
                 "title": "string",
                 "description": "string",
                 "code": "string",
                 "product": "LineOfCredit",
                 "status": "active",
                 "order": 0,
                 "individual": true,
                 "metaData": {
                     "merchant": 0,
                     "maxAmountReceived": 0,
                     "advanceRate": 0,
                     "finalPayment": 0,
                     "merchantRebate": 0,
                     "merchantFeeAfterRebate": 0,
                     "maxAmountReceivedAfterRebate": 0,
                     "customerFees30": 0,
                     "customerFees6090": "string",
                     "advancePaymentDay": "TD0",
                    "finalPaymentDay": "TD30"
                 }
               }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.AddLoanPricingPackage(BlueTape.LS.DTOs.LoanPricingPackages.ShortLoanPricingPackageDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Adds a new loan pricing package (admin function)
             </summary>
              /// <remarks>
             Sample request:
            
             
                 POST /Admin/LoanPricingPackages
             {
                 "id": "bdafd605-fb56-4d32-9c9b-3b58c174dd79",
                 "createdAt": "2024-10-10T10:21:44.277Z",
                 "createdBy": "string",
                 "updatedAt": "2024-10-10T10:21:44.277Z",
                 "updatedBy": "string",
                 "name": "string",
                 "title": "string",
                 "description": "string",
                 "code": "string",
                 "product": "LineOfCredit",
                 "status": "active",
                 "order": 0,
                 "individual": true,
                 "metaData": {
                     "merchant": 0,
                     "maxAmountReceived": 0,
                     "advanceRate": 0,
                     "finalPayment": 0,
                     "merchantRebate": 0,
                     "merchantFeeAfterRebate": 0,
                     "maxAmountReceivedAfterRebate": 0,
                     "customerFees30": 0,
                     "customerFees6090": "string",
                     "advancePaymentDay": "TD0",
                    "finalPaymentDay": "TD30"
                 }
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.UpdateCardPricingPackage(System.String,BlueTape.LS.DTOs.CardPricingPackages.ShortCardPricingPackageDto,System.String,System.Threading.CancellationToken)">
             <summary>
             Updates a card pricing package (admin function) All fields are optional. Which field is sent, that field to update.
             </summary>
             <param name="id">CardPricingPackage id</param>
              /// <remarks>
             Sample request:
            
             
                 PATCH /Admin/CardPricingPackage/{id}
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.AddCardPricingPackage(BlueTape.LS.DTOs.CardPricingPackages.ShortCardPricingPackageDto,System.String,System.Threading.CancellationToken)">
            <summary>
            Adds a new card pricing package (admin function)
            </summary>
             /// <remarks>
            Sample request:
            
                POST /Admin/CardPricingPackages
            
            </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AdminController.RefinanceLoans(BlueTape.LS.DTOs.Loan.RefinanceLoanDto,System.String,System.Threading.CancellationToken)">
            <summary>
            Refinances Loans (creates a new loan by existing loan ids)
            </summary>
             /// <remarks>
            Sample request:
            
                POST /Admin/Loans/Refinance
            
            </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.AutopayLoanController.GetUpcoming(BlueTape.Services.LMS.API.ViewModels.AutoPay.AutopayLoanQuery,System.Threading.CancellationToken)">
            <summary>
            Get loan for autopay
            </summary>
            <param name="autopayLoanQuery"></param>
            <remarks>
            Sample request:
            
                GET /AutopayLoans?UpcomingDate=2022-10-20&amp;CountDaysForUpcomming=3,
                GET /AutopayLoans
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.CalculatorController.GetPaymentPlan(BlueTape.LS.DTOs.Loan.CalculatorLoanDto,System.Threading.CancellationToken)">
            <summary>
            Calculate loan fee amount and installments by template Id
            </summary>
            <remarks>
            Sample request:
            
                GET /Calculator?Amount=1&amp;LoanTemplateId={id}
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.CalculatorController.GetPaymentPlan(System.Decimal,System.Threading.CancellationToken)">
            <summary>
            Calculate loan fee amount and installments
            </summary>
            <param name="amount">Loan amount</param>
            <remarks>
            Sample request:
            
                GET /Calculator/{amount}
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.CardPricingPackagesController.GetByFilter(BlueTape.DataAccess.Mongo.Documents.Filters.CardPricingPackagesFilter,System.Threading.CancellationToken)">
             <summary>
             Gets all card pricing packages (lists only active)
             </summary>
             <param name="query">Card pricing package params</param>
             <remarks>
             Sample request:
             
                 GET /CardPricingPackages?{filter}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.CardPricingPackagesController.GetById(System.String,System.Threading.CancellationToken)">
             <summary>
             Gets card pricing package by id (for compatibility)
             </summary>
             <param name="id">card pricing package id</param>
             <remarks>
             Sample request:
             
                 GET /CardPricingPackages/{id}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.ChangeLogController.Get(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Get change logs by loanId
             </summary>
             <param name="loanId">Loan Id</param>
             <remarks>
             Sample request:
            
                 GET /ChangeLogs?loanId={id}
             
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.CreditController.CreateCredit(BlueTape.LS.DTOs.Credit.CreateCreditDto,System.Threading.CancellationToken)">
             <summary>
             Create a credit
             </summary>
             <remarks>
             Sample request:
            
                 POST /Credits
                 {
                    "CompanyId": "12345",
                    "CreditApplicationId": "12345",
                    "ProjectId": "12345",
                    "StartDate": "2023-09-26",
                    "CreditLimit": 10000,
                    "Currency": "USD"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.Get(System.Guid,System.Nullable{System.Boolean},System.Threading.CancellationToken)">
             <summary>
             Get Loan by Id
             </summary>
             <param name="id">Loan Id</param>
             <remarks>
             Sample request:
            
                 GET /Loans/{id}
             
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.Get(BlueTape.LS.DTOs.Loan.LoanQueryDto,System.Threading.CancellationToken)">
            <summary>
            Array of Loans by query params 
            </summary>
            <param name="loanQuery"></param>
            <remarks>
            Sample request:
            
                GET /Loans?UpcomingDate=2022-10-20&amp;CountDaysForUpcoming=3,
                GET /Loans?FromDate=2022-10-20&amp;ToDate=2022-10-20&amp;LoanStatus=Pending,
                GET /Loans
                
            </remarks>
            <returns>Array of AutoPayLoan</returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.Create(BlueTape.LS.DTOs.Loan.CreateLoanDto,System.Threading.CancellationToken)">
            <summary>
            Create loan  
            </summary>
            <remarks>
            Sample request:
            
                POST /Loans
                {
                    "companyId": "string",
                    "amount": 0,
                    "loanTemplateId": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                }
            
            </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.ReplanLoan(System.Guid,BlueTape.LS.DTOs.Plan.LoanReplanDto,System.Threading.CancellationToken)">
             <summary>
             Replan loan
             </summary>
             <param name="id">Loan Id</param>
             <remarks>
             Sample request:
            
                 PUT /Loans/{id}
                 {
                    "newLoanTemplateId": "3ee4cbd6-935a-436e-b2d1-041ac9a5dc11",
                    "replanDate": "2023-05-24"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.Delete(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Mark loan as deleted  
            </summary>
            <param name="id">Loan Id</param>
            <remarks>
            Sample request:
            
                DELETE /Loans/{id}
            
            </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.DeleteByCompanyId(System.String,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            Hard delete loans by company id and date
            </summary>
            <param name="id">Company Id</param>
            <param name="date">Loans created before this date will be deleted</param>
            <remarks>
            Sample request:
            
                DELETE /Loans/Company/{id}?date=2023-11-20
            
            </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.ChangeLoanStatus(System.Guid,BlueTape.LS.DTOs.Loan.UpdateLoanDto,System.Threading.CancellationToken)">
             <summary>
             Change loan status  
             </summary>
             <param name="id">Loan Id</param>
             <remarks>
             Sample request:
             
                 PATCH /Loans/{id}
                 {
                     "status": "Started"
            
                 } 
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanController.GetLoanPayablesDetails(System.Guid,System.Nullable{System.DateOnly},System.Threading.CancellationToken)">
            <summary>
            Get Loan Payables Details
            </summary>
            <param name="id">Loan Id</param>
            <remarks>
            Sample request:
            
                GET /Loans/{id}/Payables/Details?date=2001-01-01
            
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanParametersController.GetAll(BlueTape.Services.LMS.API.Query.LoanParametersQuery,System.Threading.CancellationToken)">
             <summary>
             Get all parameter templates for specific loan
             </summary>
             <param name="query">Loan Id</param>
             <remarks>
             Sample request:
             
                 GET /LoanParameters?LoanId={id}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanParametersController.GetById(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Get parameter template by Id
             </summary>
             <param name="id">Loan Parameters Id</param>
             <remarks>
             Sample request:
            
                 GET /LoanParameters/{id}
             
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanPricingPackagesController.GetByFilter(BlueTape.LS.Domain.Models.LoanPricingPackagesFilter,System.Threading.CancellationToken)">
             <summary>
             Gets all loan pricing packages (lists only active)
             </summary>
             <param name="query">Loan Id</param>
             <remarks>
             Sample request:
             
                 GET /LoanPricingPackages?{filter}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanPricingPackagesController.GetById(System.String,System.Threading.CancellationToken)">
             <summary>
             Gets loan pricing package by id (for compatibility)
             </summary>
             <param name="id">Loan pricing package id</param>
             <remarks>
             Sample request:
             
                 GET /LoanPricingPackages/{id}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanReceivableController.Get(BlueTape.Services.LMS.API.Query.LoanReceivableQuery,System.Threading.CancellationToken)">
            <summary>
            Get array of Receivables by query params 
            </summary>
            <param name="loanReceivableQuery">Loan Id</param>
            <remarks>
            Sample request:
            
                GET /LoanReceivables?LoanId={id}
                GET /LoanReceivables
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanReceivableController.GetReceivablesPaymentsTimelineItems(BlueTape.Services.LMS.API.Query.TimelineItemsQuery,System.Threading.CancellationToken)">
            <summary>
            Get payables timeline items of loan by Loan Id 
            </summary>
            <param name="timelineItemsQuery">Loan Id</param>
            <remarks>
            Sample request:
            
                GET /LoanReceivables/Payments?LoanId={id}
                
            </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanReceivableController.GetByCompanyIdAndStatusWithPagination(BlueTape.Services.LMS.Infrastructure.Pagination.UpcomingDueReceivablesByCompanyIdQuery,System.Threading.CancellationToken)">
            <summary>
            Get array of Receivables by query params 
            </summary>
            <param name="query">Params to search loan receivables</param>
            <remarks>
            Sample request:
            
                POST /LoanReceivables/Company
                {
                   "pageNumber": 0,
                   "pageSize": 0,
                   "companyId": "string",
                   "dateFrom": "2024-01-10",
                   "dateTo": "2024-01-10",
            "loanIds": [ 
                   "3fa85f64-5717-4562-b3fc-2c963f66afa6"
                   ],
                   "isOverdue": true
               }
            /// </remarks>
            <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanTemplateController.GetById(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Get loan template by Id
             </summary>
             <param name="id">Loan template Id</param>
             <remarks>
             Sample request:
             
                 GET /LoanTemplates/{id}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanTemplateController.Get(BlueTape.LS.Domain.Models.LoanTemplatesQuery,System.Threading.CancellationToken)">
             <summary>
             Get all loan templates
             </summary>
             <param name="query">Loan template query</param>
             <remarks>
             Sample request:
             
                 GET /LoanTemplates?LegacyId={legacyId}&amp;Code={code}&amp;Product={product}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanTemplateController.Post(BlueTape.LS.DTOs.LoanTemplates.ShortLoanTemplateDto,System.Threading.CancellationToken)">
             <summary>
             Create loan template
             </summary>
             <param name="loanTemplateDto"></param>
             
             <remarks>
             Sample request:
            
                 POST /LoanTemplates
                 {
                    "LoanFeePercentage": 0.092,
                    "InstallmentsNumber": 1,
                    "PaymentDelayInDays": 120,
                    "PaymentIntervalInDays": 7,
                    "MinimumLateFeeAmount" : 35.5,
                    "LateFeePercentage": 1.5,
                    "GracePeriodInDays": 20,
                    "EarlyPayPeriod": 5,
                    "TotalDurationInDays": 10,
                    "Code": "120",
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanTemplateController.Put(System.Guid,BlueTape.LS.DTOs.LoanTemplates.ShortLoanTemplateDto,System.Threading.CancellationToken)">
             <summary>
             Update loan template
             </summary>
             <param name="id">Loan Id</param>
             <param name="loanTemplateDto"></param>
             <remarks>
             Sample request:
            
                 PUT /LoanTemplates/{id}
                 {
                    "LoanFeePercentage": 0.092,
                    "InstallmentsNumber": 1,
                    "PaymentDelayInDays": 120,
                    "PaymentIntervalInDays": 7,
                    "MinimumLateFeeAmount" : 35.5,
                    "LateFeePercentage": 1.5,
                    "GracePeriodInDays": 20,
                    "EarlyPayPeriod": 5,
                    "TotalDurationInDays": 10,
                    "Code": "120",
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.LoanTemplateController.Delete(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Remove loan template
             </summary>
             <param name="id">Loan template Id</param>
             <remarks>
             Sample request:
             
                 DELETE /LoanTemplates/{id}
            
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.OverDueDetectorController.MarkAsOverdue(System.Threading.CancellationToken)">
             <summary>
             Start Overdue Process
             </summary>
             <remarks>
             Sample request:
             
                 PATCH /OverDueDetector
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.OverDueDetectorController.MarkAsOverdue(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Start Overdue Process for specific loan
             </summary>
             <remarks>
             Sample request:
             
                 PATCH /OverDueDetector/{id}
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.PaymentController.UpdatePayment(System.Guid,BlueTape.LS.DTOs.Payment.UpdatePaymentDto,System.Threading.CancellationToken)">
             <summary>
             Change payment status or transaction number
             </summary>
             <param name="updatePaymentViewModel"></param>
             <param name="id">Payment id</param>
             <remarks>
             Sample request:
            
                 PATCH /Payments/{id}
                 {
                    "Status": "Success"
                 }
            
                 PATCH /Payments/{id}
                 {
                    "transactionNumber": ""
                 }
             
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.PaymentController.GetByLoanId(BlueTape.Services.LMS.API.Query.PaymentQuery,System.Threading.CancellationToken)">
             <summary>
             Get payments by Loan id 
             </summary>
             <param name="paymentQuery">Loan Id</param>
             <remarks>
             Sample request:
            
                 GET /Payments?LoanId={id}
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.PaymentController.PerformPayment(BlueTape.LS.DTOs.Payment.CreatePaymentDto,System.Threading.CancellationToken)">
             <summary>
             Create processing payment
             </summary>
             <param name="createPaymentViewModel"></param>
             <remarks>
             Sample request:
            
                 POST /Payments
                 {
                    "LoanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                    "Amount": 100,
                    "Type": "AutoDebit"
                 }
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.PenaltyDetectorController.IssuePenaltyInterest(System.Threading.CancellationToken)">
             <summary>
             Start Penalty Issuing Process
             </summary>
             <remarks>
             Sample request:
             
                 PATCH /PenaltyDetector
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.PenaltyDetectorController.IssuePenaltyInterest(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Start Penalty Issuing Process for certain loan
             </summary>
             <remarks>
             Sample request:
             
                 PATCH /PenaltyDetector/3fa85f64-5717-4562-b3fc-2c963f66afa6
            
             </remarks>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.QaController.ReplaceLoanReceivables(System.Guid,BlueTape.LS.DTOs.LoanReceivable.CreateLoanReceivablesDto,System.Threading.CancellationToken)">
            
             <summary>
             Replace LoanReceivables, especially for QA
             </summary>
             <param name="id"></param>
             <param name="loanReceivablesModel"></param>
             <remarks>
             Sample request:
            
                 POST /Qa/Loans/{id}
                 "loanReceivables":
                 {
                     [
                         {
                             "expectedDate": "2023-06-07",
                             "actualDate": "2023-06-07",
                             "expectedAmount": 100,
                             "paidAmount": 0,
                             "adjustDate": "2023-06-07",
                             "adjustAmount": 0,
                             "scheduleStatus": "Current",
                             "status": "Pending",
                             "type": "Installment",
                             "loanId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
                             "createdBy": "string",
                             "updatedBy": "string"
                         }
                     ]
                 }
             </remarks>
             <returns></returns>
        </member>
        <member name="M:BlueTape.Services.LMS.API.Controllers.СreditStatusDetectorController.TriggerCreditStatusDetector(System.Guid,System.Threading.CancellationToken)">
             <summary>
             Start credit status detector for certain credit
             </summary>
             <remarks>
             Sample request:
             
                 PATCH /CreditStatusDetector/{id}
            
             </remarks>
        </member>
        <member name="T:BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee.FeeValidatorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee.FeeValidatorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee.FeeValidatorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee.FeeValidatorResources.ExpectedAmountGreaterThenZero">
            <summary>
              Looks up a localized string similar to ExpectedAmount should be greater than 0.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LatePaymentFee.FeeValidatorResources.PaymentIdEmpty">
            <summary>
              Looks up a localized string similar to PaymentId can&apos;t be empty.
            </summary>
        </member>
        <member name="T:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.EarlyPayPeriodGreaterThanZero">
            <summary>
              Looks up a localized string similar to EarlyPayPeriod should be greater than or equal 0.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.FeePercentBetweenZeroAndOne">
            <summary>
              Looks up a localized string similar to FeePercent should be between 0 and 1.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.GracePeriodGreaterThanZero">
            <summary>
              Looks up a localized string similar to GracePeriod should be greater than or equal 0.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.InstallmentsNumberGreaterThanZero">
            <summary>
              Looks up a localized string similar to InstallmentsNumber should be greater than 0.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.LateFeePercentBetweenZeroAndOne">
            <summary>
              Looks up a localized string similar to LateFeePercent should be between 0 and 1.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.PaymentDelayInDaysGreaterThanZero">
            <summary>
              Looks up a localized string similar to PaymentDelayInDays should be greater than or equal 0.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.LoanTemplate.LoanTemplateValidatorResources.PaymentIntervalInDaysGreaterThanZero">
            <summary>
              Looks up a localized string similar to PaymentIntervalInDays should be greater than or equal 0.
            </summary>
        </member>
        <member name="T:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources.AmountGreaterThanZero">
            <summary>
              Looks up a localized string similar to Amount should be greater than 0.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources.CompanyIdEmpty">
            <summary>
              Looks up a localized string similar to CompanyId can&apos;t be empty.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources.EinHashEmpty">
            <summary>
              Looks up a localized string similar to Ein hash can&apos;t be empty.
            </summary>
        </member>
        <member name="P:BlueTape.Services.LMS.API.Resources.ViewModels.Loan.LoanValidatorResources.LoanTemplateIdEmpty">
            <summary>
              Looks up a localized string similar to LoanTemplateId can&apos;t be empty.
            </summary>
        </member>
    </members>
</doc>
