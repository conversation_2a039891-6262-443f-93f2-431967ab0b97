[{"name": "CreditMigrationFunction", "scriptFile": "BlueTape.Functions.LMS.CreditStatusDetector.dll", "entryPoint": "BlueTape.Functions.LMS.CreditStatusDetector.CreditMigrationFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 1 1 1 *", "properties": {}}]}, {"name": "CreditStatusConsumer", "scriptFile": "BlueTape.Functions.LMS.CreditStatusDetector.dll", "entryPoint": "BlueTape.Functions.LMS.CreditStatusDetector.CreditStatusConsumer.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "message", "direction": "In", "type": "serviceBusTrigger", "queueName": "%creditStatusQueueName%", "connection": "creditStatusQueueConnection", "cardinality": "One", "properties": {"supportsDeferredBinding": "True"}}]}, {"name": "CreditStatusDetectorFunction", "scriptFile": "BlueTape.Functions.LMS.CreditStatusDetector.dll", "entryPoint": "BlueTape.Functions.LMS.CreditStatusDetector.CreditStatusDetector.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 0 13 * * *", "properties": {}}]}, {"name": "CheckDownPaymentExpirationFunction", "scriptFile": "BlueTape.Functions.LMS.OverDueDetector.dll", "entryPoint": "BlueTape.Functions.LMS.OverDueDetector.CheckDownPaymentExpirationFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 30 17 * * *", "properties": {}}]}, {"name": "OverDueDetectorFunction", "scriptFile": "BlueTape.Functions.LMS.OverDueDetector.dll", "entryPoint": "BlueTape.Functions.LMS.OverDueDetector.OverDueDetectorFunction.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 45 12 * * *", "properties": {}}]}, {"name": "PenaltyDetector", "scriptFile": "BlueTape.Functions.LMS.PenaltyDetector.dll", "entryPoint": "BlueTape.Functions.LMS.PenaltyDetector.PenaltyDetector.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 40 9 * * *", "properties": {}}]}, {"name": "LoanTapeReport", "scriptFile": "BlueTape.Functions.LMS.Reports.LoanTapeReport.dll", "entryPoint": "BlueTape.Functions.LMS.Reports.LoanTapeReport.LoanTapeReport.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 0 7 * * *", "properties": {}}]}, {"name": "MissingLoanTapeReportFilesJob", "scriptFile": "BlueTape.Functions.LMS.Reports.LoanTapeReport.dll", "entryPoint": "BlueTape.Functions.LMS.Reports.LoanTapeReport.MissingLoanTapeReportFiles.Run", "language": "dotnet-isolated", "properties": {"IsCodeless": false}, "bindings": [{"name": "myTimer", "direction": "In", "type": "timer<PERSON><PERSON>ger", "schedule": "0 0 10 * * *", "properties": {}}]}]