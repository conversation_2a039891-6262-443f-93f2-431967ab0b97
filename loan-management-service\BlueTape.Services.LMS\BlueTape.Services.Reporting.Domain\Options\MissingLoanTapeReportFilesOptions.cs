namespace BlueTape.Services.Reporting.Domain.Options;

public class MissingLoanTapeReportFilesOptions
{
    /// <summary>
    /// Number of days to check for missing reports
    /// </summary>
    public int DaysToCheck { get; set; } = 0;

    /// <summary>
    /// Funding sources to check for reports
    /// Values should match FundingSource enum names
    /// </summary>
    public string[] FundingSources { get; set; } = ["Arcadia", "Raistone"];

    /// <summary>
    /// Report types to check for each funding source
    /// Values should match the ReportType enum names without the "LoanTape" prefix
    /// </summary>
    public string[] ReportTypes { get; set; } = ["Factoring", "AionLoanSales", "FinalPayment"];
}
