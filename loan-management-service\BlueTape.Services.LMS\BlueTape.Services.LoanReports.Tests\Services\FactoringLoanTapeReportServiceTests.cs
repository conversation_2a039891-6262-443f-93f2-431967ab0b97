﻿using BlueTape.Services.LMS.Application.Tests.Attributes;
using BlueTape.Services.LMS.DataAccess.Abstractions.Repositories;
using BlueTape.Services.LMS.Domain.Entities;
using BlueTape.Services.LMS.Domain.Enums;
using BlueTape.Services.Reporting.Abstractions.Builders;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Constants;
using BlueTape.Services.Reporting.Domain.Enums;
using BlueTape.Services.Reporting.Models.FactoringReports;
using BlueTape.Services.Reporting.Models.LoanReportsHistory;
using BlueTape.Services.Reporting.Models.LoanTapeReports;
using BlueTape.Services.Reporting.Models.ReportFileGeneration;
using BlueTape.Services.Reporting.Services;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Moq;
using System.Linq.Expressions;

namespace BlueTape.Services.LoanReports.Tests.Services;

public class FactoringLoanTapeReportServiceTests
{
    private readonly FactoringLoanTapeReportService _factoringLoanTapeReportService;

    private readonly Mock<ILoanTapeReportRowDataBuilder> _rowDataBuilderMock = new();
    private readonly Mock<IHeaderDataBuilder> _headerDataBuilderMock = new();
    private readonly Mock<ILoanReportsHistoryService> _loanReportsHistoryServiceMock = new();
    private readonly Mock<ILoanRepository> _loanRepositoryMock = new();
    private readonly Mock<IReportsUploadService> _reportsUploadService = new();
    private readonly Mock<IReportFileGenerationService> _reportFileGenerationService = new();
    private readonly Mock<IReportsNotificationsService> _reportsNotificationsService = new();
    private readonly Mock<ILogger<FactoringLoanTapeReportService>> _loggerMock = new();
    private readonly Mock<IDateProvider> _dateProviderMock = new();
    private readonly DateOnly _currentDate = DateOnly.FromDateTime(DateTime.UtcNow);

    public FactoringLoanTapeReportServiceTests()
    {

        _dateProviderMock.Setup(x => x.CurrentDate).Returns(_currentDate);

        _factoringLoanTapeReportService = new FactoringLoanTapeReportService(
            _rowDataBuilderMock.Object,
            _headerDataBuilderMock.Object,
            _loanReportsHistoryServiceMock.Object,
            _loanRepositoryMock.Object,
            _reportsUploadService.Object,
            _reportFileGenerationService.Object,
            _reportsNotificationsService.Object,
            _dateProviderMock.Object,
            _loggerMock.Object);
    }

    [Theory]
    [InlineData(FundingSource.Arcadia)]
    [InlineData(FundingSource.Raistone)]
    [InlineData(FundingSource.Aion)]
    public async Task GenerateReport_NoGeneratedReportsExists_DoesNotReturnApplicableLoans(FundingSource fundingSource)
    {
        _loanReportsHistoryServiceMock.Setup(x => x.GetByReportType(ReportType.LoanTapeFactoring, default))
            .ReturnsAsync(new List<LoanReportsHistory>());
        _loanReportsHistoryServiceMock.Setup(x => x.Insert(It.IsAny<CreateLoanReportsHistoryModel>(), default))
            .ReturnsAsync(new LoanReportsHistory()
            {
                Id = Guid.NewGuid()
            });

        _loanRepositoryMock.Setup(x =>
                x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0, int.MaxValue))
            .ReturnsAsync(new List<LoanEntity>());

        _reportFileGenerationService.Setup(x => x.GenerateCsvReportFilesAsync(It.IsAny<CreateFactoringReportModel>(),
                It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))))
            .ReturnsAsync(new ReportFile { FileName = $"{LoanTapeReportConstants.FactoringSalesReportFileNamePrefix}_2024-09-23.csv" });

        _reportFileGenerationService.Setup(x => x.GenerateXlsReportFilesAsync(It.IsAny<CreateFactoringReportModel>(),
                It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))))
            .ReturnsAsync(new ReportFile { FileName = $"{LoanTapeReportConstants.FactoringSalesReportFileNamePrefix}_2024-09-23.xlsx" });

        await _factoringLoanTapeReportService.GenerateReport(fundingSource, default);

        _loanReportsHistoryServiceMock.Verify(x => x.GetByReportType(ReportType.LoanTapeFactoring, default),
            Times.Once);
        _loanRepositoryMock.Verify(
            x => x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0, int.MaxValue),
            Times.Once);

        _reportFileGenerationService.Verify(x => x.GenerateCsvReportFilesAsync(
            It.Is<CreateFactoringReportModel>(m => m.Rows.Count == 0),
            It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))), Times.Once);
        _reportFileGenerationService.Verify(x => x.GenerateXlsReportFilesAsync(
            It.Is<CreateFactoringReportModel>(m => m.Rows.Count == 0),
            It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))), Times.Once);
        _reportsNotificationsService.Verify(x => x.SendLoanTapeReportGeneratedNotification(
            It.Is<GeneratedLoanTapeReportModel>(m =>
                m.TotalAmount == 0
                && m.FileName.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix)
                && m.ReportDate == _currentDate), fundingSource,
            default), Times.Once);
        _rowDataBuilderMock.Verify(x => x.BuildFactoringReportRowsData(It.IsAny<IReadOnlyCollection<LoanEntity>>(), default), Times.Never);

    }

    [Theory, AutoDataWithDateOnly]

    public async Task GenerateReport_ApplicableLoanExist_GeneratesDataForReport(List<LoanEntity> loans,
        List<FactoringReportRowModel> rowsData, FactoringReportHeaderData headerData, FundingSource fundingSource)
    {
        var loanIds = loans.Select(x => x.Id);
        var loansForReportIds = loanIds.Skip(1).Take(2).ToList();

        var report = new LoanReportsHistory()
        {
            Id = Guid.NewGuid()
        };
        _loanReportsHistoryServiceMock.Setup(x => x.GetByReportType(ReportType.LoanTapeFactoring, default))
            .ReturnsAsync(new List<LoanReportsHistory>());
        _loanRepositoryMock
            .Setup(x => x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0,
                int.MaxValue)).ReturnsAsync(loans);
        _rowDataBuilderMock.Setup(x => x.BuildFactoringReportRowsData(It.Is<IReadOnlyCollection<LoanEntity>>(
            x => x.Select(loan => loan.Id).SequenceEqual(loansForReportIds)), default)).ReturnsAsync(rowsData);
        _headerDataBuilderMock.Setup(x => x.BuildForFactoring(rowsData, _currentDate)).Returns(headerData);
        _loanReportsHistoryServiceMock.Setup(x => x.Insert(It.Is<CreateLoanReportsHistoryModel>(
                x => x.LoanReportsHistoryItems.Select(item => item.LoanId).SequenceEqual(loansForReportIds)
                     && x.Report == ReportType.LoanTapeFactoring), default))
            .ReturnsAsync(report);
        _reportFileGenerationService.Setup(x => x.GenerateCsvReportFilesAsync(
                It.Is<CreateFactoringReportModel>(m => m.Rows == rowsData),
                It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))))
            .ReturnsAsync(new ReportFile { FileName = $"{LoanTapeReportConstants.FactoringSalesReportFileNamePrefix}_2024-09-23.csv" });
        _reportFileGenerationService.Setup(x => x.GenerateXlsReportFilesAsync(
                It.Is<CreateFactoringReportModel>(m => m.Rows == rowsData),
                It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))))
            .ReturnsAsync(new ReportFile { FileName = $"{LoanTapeReportConstants.FactoringSalesReportFileNamePrefix}_2024-09-23.xlsx" });
        _loanRepositoryMock.Setup(x => x.GetLoanRelations(It.IsAny<Expression<Func<LoanRelationsEntity, bool>>>(), default)).ReturnsAsync(new List<LoanRelationsEntity>()
        {
            new LoanRelationsEntity()
            {
                ChildLoanId = loans.First().Id
            }
        });

        //Act
        await _factoringLoanTapeReportService.GenerateReport(fundingSource, default);

        //Assert
        _loanReportsHistoryServiceMock.Verify(x => x.GetByReportType(ReportType.LoanTapeFactoring, default),
            Times.Once);
        _rowDataBuilderMock.Verify(x => x.BuildFactoringReportRowsData(It.Is<IReadOnlyCollection<LoanEntity>>(x => x.Select(loan => loan.Id).SequenceEqual(loansForReportIds)), default), Times.Once);
        _headerDataBuilderMock.Verify(x => x.BuildForFactoring(rowsData, _currentDate), Times.Once);
        _loanRepositoryMock.Verify(
            x => x.GetLoansWithDetailedInfo(It.IsAny<Expression<Func<LoanEntity, bool>>>(), default, 0, int.MaxValue),
            Times.Once);
        _reportsUploadService.Verify(
            x => x.UploadLoanTapeToS3(It.IsAny<byte[]>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);
        _reportFileGenerationService.Verify(x => x.GenerateCsvReportFilesAsync(
            It.Is<CreateFactoringReportModel>(m => m.Rows == rowsData),
            It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))), Times.Once);
        _reportFileGenerationService.Verify(x => x.GenerateXlsReportFilesAsync(
            It.Is<CreateFactoringReportModel>(m => m.Rows == rowsData),
            It.Is<string>(s => s.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix))), Times.Once);
        _reportsNotificationsService.Verify(x => x.SendLoanTapeReportGeneratedNotification(
            It.Is<GeneratedLoanTapeReportModel>(m =>
                m.TotalAmount == headerData.TotalPurchaseAmount
                && m.FileName.StartsWith(LoanTapeReportConstants.FactoringSalesReportFileNamePrefix)
                && m.ReportDate == headerData.ReportDate), FundingSource.Arcadia,
            default), Times.Once);
        _loanRepositoryMock.Verify(x => x.GetLoanRelations(It.IsAny<Expression<Func<LoanRelationsEntity, bool>>>(), default), Times.Once);
    }
}