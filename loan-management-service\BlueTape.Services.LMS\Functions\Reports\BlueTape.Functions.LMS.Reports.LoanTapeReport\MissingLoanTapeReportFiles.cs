using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Constants;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace BlueTape.Functions.LMS.Reports.LoanTapeReport;

public class MissingLoanTapeReportFiles(IServiceScopeFactory serviceScopeFactory)
{
    [Function(LoanTapeReportConstants.MissingLoanTapeReportFilesJobName)]
    public async Task Run([TimerTrigger("0 0 10 * * *")] TimerInfo myTimer, CancellationToken ct)
    {
        await using var scope = serviceScopeFactory.CreateAsyncScope();

        var checkService = scope.ServiceProvider.GetRequiredService<IMissingLoanTapeReportFilesService>();
        var traceIdAccessor = scope.ServiceProvider.GetRequiredService<ITraceIdAccessor>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<MissingLoanTapeReportFiles>>();

        traceIdAccessor.TraceId = $"{Guid.NewGuid()}-{LoanTapeReportConstants.MissingLoanTapeReportFilesJobName}";

        logger.LogInformation("Starting MissingLoanTapeReportFilesJob at {Time}", DateTime.UtcNow);

        await checkService.CheckMissingReportFiles(ct);

        logger.LogInformation("Completed MissingLoanTapeReportFilesJob at {Time}", DateTime.UtcNow);
    }
}
