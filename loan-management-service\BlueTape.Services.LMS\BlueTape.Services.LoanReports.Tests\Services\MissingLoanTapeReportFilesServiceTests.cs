using BlueTape.Common.Extensions.Abstractions;
using BlueTape.Services.Reporting.Abstractions.Services;
using BlueTape.Services.Reporting.Domain.Options;
using BlueTape.Services.Reporting.Services;
using BlueTape.SNS.SlackNotification.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace BlueTape.Services.LoanReports.Tests.Services;

public class MissingLoanTapeReportFilesServiceTests
{
    private readonly Mock<IReportsUploadService> _reportsUploadServiceMock;
    private readonly Mock<ISlackNotificationService> _notificationServiceMock;
    private readonly MissingLoanTapeReportFilesService _service;
    private readonly MissingLoanTapeReportFilesOptions _options;

    public MissingLoanTapeReportFilesServiceTests()
    {
        _reportsUploadServiceMock = new Mock<IReportsUploadService>();
        _notificationServiceMock = new Mock<ISlackNotificationService>();
        var traceIdAccessorMock = new Mock<ITraceIdAccessor>();
        var dateProviderMock = new Mock<IDateProvider>();
        var loggerMock = new Mock<ILogger<MissingLoanTapeReportFilesService>>();
        var optionsMock = new Mock<IOptions<MissingLoanTapeReportFilesOptions>>();

        var currentDate = new DateOnly(2023, 10, 15);
        dateProviderMock.Setup(dp => dp.CurrentDate).Returns(currentDate);
        traceIdAccessorMock.Setup(t => t.TraceId).Returns("test-trace-id");

        _options = new MissingLoanTapeReportFilesOptions
        {
            DaysToCheck = 2,
            FundingSources = ["Arcadia", "Raistone"],
            ReportTypes = ["Factoring", "AionLoanSales", "FinalPayment"]
        };

        optionsMock.Setup(o => o.Value).Returns(_options);

        _service = new MissingLoanTapeReportFilesService(
            _reportsUploadServiceMock.Object,
            _notificationServiceMock.Object,
            traceIdAccessorMock.Object,
            dateProviderMock.Object,
            optionsMock.Object,
            loggerMock.Object);
    }

    [Fact]
    public async Task CheckMissingReportFiles_WhenAllFilesExist_DoesNotSendNotification()
    {
        // Arrange
        _reportsUploadServiceMock
            .Setup(s => s.IsFileExistInS3Bucket(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _service.CheckMissingReportFiles(CancellationToken.None);

        // Assert
        _notificationServiceMock.Verify(
            n => n.Notify(It.IsAny<EventMessageBody>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task CheckMissingReportFiles_WhenSomeFilesAreMissing_SendsNotificationWithMissingFiles()
    {
        // Arrange
        var missingFile = "FACTORING_SALES_REPORT_Arcadia_2023-10-15.csv";

        _reportsUploadServiceMock
            .Setup(s => s.IsFileExistInS3Bucket(missingFile, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _reportsUploadServiceMock
            .Setup(s => s.IsFileExistInS3Bucket(It.Is<string>(s => s != missingFile), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _service.CheckMissingReportFiles(CancellationToken.None);

        // Assert
        _notificationServiceMock.Verify(
            n => n.Notify(It.Is<EventMessageBody>(body => body.Message.Contains(missingFile)),
                It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task CheckMissingReportFiles_ChecksTheCorrectNumberOfDays()
    {
        // Arrange
        _reportsUploadServiceMock
            .Setup(s => s.IsFileExistInS3Bucket(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _service.CheckMissingReportFiles(CancellationToken.None);

        // Assert - should check today and DaysToCheck days back (DaysToCheck + 1 days total)
        _reportsUploadServiceMock.Verify(
            s => s.IsFileExistInS3Bucket(It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Exactly((_options.DaysToCheck + 1) * _options.FundingSources.Length * _options.ReportTypes.Length));
    }

    [Fact]
    public async Task CheckMissingReportFiles_ChecksAllConfiguredFundingSources()
    {
        // Arrange
        _reportsUploadServiceMock
            .Setup(s => s.IsFileExistInS3Bucket(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _service.CheckMissingReportFiles(CancellationToken.None);

        // Assert
        foreach (var fundingSource in _options.FundingSources)
        {
            _reportsUploadServiceMock.Verify(
                s => s.IsFileExistInS3Bucket(It.Is<string>(file => file.Contains(fundingSource)),
                    It.IsAny<CancellationToken>()),
                Times.AtLeast(_options.ReportTypes.Length));
        }
    }

    [Fact]
    public async Task CheckMissingReportFiles_ChecksAllConfiguredReportTypes()
    {
        // Arrange
        _reportsUploadServiceMock
            .Setup(s => s.IsFileExistInS3Bucket(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await _service.CheckMissingReportFiles(CancellationToken.None);

        // Assert
        _reportsUploadServiceMock.Verify(
            s => s.IsFileExistInS3Bucket(It.Is<string>(file => file.Contains("FACTORING_SALES_REPORT")),
                It.IsAny<CancellationToken>()),
            Times.AtLeast((_options.DaysToCheck + 1) * _options.FundingSources.Length));

        _reportsUploadServiceMock.Verify(
            s => s.IsFileExistInS3Bucket(It.Is<string>(file => file.Contains("AION_LOAN_SALES_REPORT")),
                It.IsAny<CancellationToken>()),
            Times.AtLeast((_options.DaysToCheck + 1) * _options.FundingSources.Length));

        _reportsUploadServiceMock.Verify(
            s => s.IsFileExistInS3Bucket(It.Is<string>(file => file.Contains("FINAL_PAYMENT_REPORT")),
                It.IsAny<CancellationToken>()),
            Times.AtLeast((_options.DaysToCheck + 1) * _options.FundingSources.Length));
    }
}
